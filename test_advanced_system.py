"""
اختبار شامل للنظام المتقدم
"""

import sys
import time
from datetime import datetime
import traceback

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔄 اختبار استيراد الوحدات...")
    
    try:
        from advanced_config import MARKETS_CONFIG, ANALYSIS_CONFIG
        print("✅ تم استيراد advanced_config")
        
        from advanced_data_collector import AdvancedDataCollector
        print("✅ تم استيراد AdvancedDataCollector")
        
        from advanced_analyzer import AdvancedMarketAnalyzer
        print("✅ تم استيراد AdvancedMarketAnalyzer")
        
        from advanced_reporter import AdvancedReporter
        print("✅ تم استيراد AdvancedReporter")
        
        from advanced_models import ContinuousLearningSystem
        print("✅ تم استيراد ContinuousLearningSystem")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        traceback.print_exc()
        return False

def test_data_collection():
    """اختبار جمع البيانات المتقدم"""
    print("\n🔄 اختبار جمع البيانات المتقدم...")
    
    try:
        from advanced_data_collector import AdvancedDataCollector
        
        collector = AdvancedDataCollector()
        
        # اختبار جلب بيانات الأسهم
        print("📈 اختبار بيانات الأسهم...")
        stock_data = collector.fetch_multi_timeframe_data('AAPL', 'stocks')
        
        if stock_data:
            print(f"✅ تم جلب بيانات AAPL لـ {len(stock_data)} إطار زمني")
            for timeframe, data in stock_data.items():
                if data is not None:
                    print(f"   - {timeframe}: {len(data)} صف")
        else:
            print("⚠️ لم يتم جلب بيانات الأسهم")
        
        # اختبار جلب بيانات العملات
        print("💱 اختبار بيانات العملات...")
        forex_data = collector.fetch_forex_data(['EURUSD=X'])
        
        if forex_data:
            print(f"✅ تم جلب بيانات العملات لـ {len(forex_data)} رمز")
        else:
            print("⚠️ لم يتم جلب بيانات العملات")
        
        # اختبار جلب بيانات السلع
        print("🥇 اختبار بيانات السلع...")
        commodities_data = collector.fetch_commodities_data(['GC=F'])
        
        if commodities_data:
            print(f"✅ تم جلب بيانات السلع لـ {len(commodities_data)} رمز")
        else:
            print("⚠️ لم يتم جلب بيانات السلع")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في جمع البيانات: {str(e)}")
        traceback.print_exc()
        return False

def test_analysis_system():
    """اختبار نظام التحليل المتقدم"""
    print("\n🔄 اختبار نظام التحليل المتقدم...")
    
    try:
        from advanced_data_collector import AdvancedDataCollector
        from advanced_analyzer import AdvancedMarketAnalyzer
        
        # جلب البيانات
        collector = AdvancedDataCollector()
        data = collector.fetch_multi_timeframe_data('AAPL', 'stocks')
        
        if not data:
            print("⚠️ لا توجد بيانات للتحليل")
            return False
        
        # تحليل البيانات
        analyzer = AdvancedMarketAnalyzer()
        analysis = analyzer.analyze_multi_timeframe(data, 'AAPL')
        
        if analysis:
            print("✅ تم إكمال التحليل المتقدم")
            
            # عرض نتائج التحليل
            for timeframe, timeframe_analysis in analysis.items():
                if timeframe != 'multi_timeframe_confluence':
                    print(f"   📊 {timeframe}:")
                    
                    if 'trend_analysis' in timeframe_analysis:
                        trend = timeframe_analysis['trend_analysis']
                        print(f"      - الاتجاه: {trend.get('primary_trend', 'غير محدد')}")
                        print(f"      - القوة: {trend.get('trend_strength', 'غير محدد')}")
                    
                    if 'momentum_analysis' in timeframe_analysis:
                        momentum = timeframe_analysis['momentum_analysis']
                        print(f"      - RSI: {momentum.get('rsi_signal', 'غير محدد')}")
                        print(f"      - MACD: {momentum.get('macd_signal', 'غير محدد')}")
            
            # التوافق بين الإطارات
            if 'multi_timeframe_confluence' in analysis:
                confluence = analysis['multi_timeframe_confluence']
                print(f"   🎯 التوافق العام: {confluence.get('overall_trend', 'غير محدد')}")
            
            return True
        else:
            print("❌ فشل في التحليل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في نظام التحليل: {str(e)}")
        traceback.print_exc()
        return False

def test_reporting_system():
    """اختبار نظام التقارير"""
    print("\n🔄 اختبار نظام التقارير...")
    
    try:
        from advanced_reporter import AdvancedReporter
        
        # إنشاء بيانات تحليل مثال
        sample_analysis = {
            '5min': {
                'trend_analysis': {
                    'primary_trend': 'صاعد',
                    'trend_strength': 'متوسط'
                },
                'momentum_analysis': {
                    'rsi_signal': 'متوازن',
                    'rsi_value': 55,
                    'macd_signal': 'لا توجد إشارة'
                },
                'support_resistance': {
                    'pivot': 150.0,
                    'resistance_1': 155.0,
                    'support_1': 145.0
                }
            },
            'multi_timeframe_confluence': {
                'overall_trend': 'صاعد',
                'trend_strength': 'متوسط'
            }
        }
        
        # إنشاء التقرير
        reporter = AdvancedReporter()
        report = reporter.generate_comprehensive_report('AAPL', sample_analysis, 'stocks')
        
        if report:
            print("✅ تم إنشاء التقرير بنجاح")
            
            # عرض ملخص التقرير
            exec_summary = report.get('executive_summary', {})
            print(f"   📋 التوصية الرئيسية: {exec_summary.get('main_recommendation', 'غير محدد')}")
            print(f"   📊 مستوى الثقة: {exec_summary.get('confidence_level', 0):.1%}")
            print(f"   ⚠️ مستوى المخاطر: {exec_summary.get('risk_level', 'غير محدد')}")
            
            # اختبار تقرير HTML
            html_path = reporter.generate_html_report('AAPL', report)
            if html_path:
                print(f"   📄 تم إنشاء تقرير HTML: {html_path}")
            
            return True
        else:
            print("❌ فشل في إنشاء التقرير")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في نظام التقارير: {str(e)}")
        traceback.print_exc()
        return False

def test_advanced_models():
    """اختبار النماذج المتقدمة"""
    print("\n🔄 اختبار النماذج المتقدمة...")
    
    try:
        from advanced_models import ContinuousLearningSystem
        
        # إنشاء نظام التعلم المستمر
        learning_system = ContinuousLearningSystem('AAPL', 'stocks')
        
        # تهيئة النماذج
        input_shape = (100, 60, 15)  # (batch_size, sequence_length, features)
        
        if learning_system.initialize_models(input_shape):
            print("✅ تم تهيئة النماذج المتقدمة بنجاح")
            
            # عرض النماذج المتاحة
            print("   🧠 النماذج المتاحة:")
            for model_name in learning_system.models.keys():
                print(f"      - {model_name}")
            
            return True
        else:
            print("❌ فشل في تهيئة النماذج")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في النماذج المتقدمة: {str(e)}")
        traceback.print_exc()
        return False

def test_complete_workflow():
    """اختبار سير العمل الكامل"""
    print("\n🔄 اختبار سير العمل الكامل...")
    
    try:
        from advanced_data_collector import AdvancedDataCollector
        from advanced_analyzer import AdvancedMarketAnalyzer
        from advanced_reporter import AdvancedReporter
        
        symbol = 'AAPL'
        market_type = 'stocks'
        
        print(f"📊 تحليل شامل لـ {symbol}...")
        
        # 1. جمع البيانات
        print("   1️⃣ جمع البيانات...")
        collector = AdvancedDataCollector()
        data = collector.fetch_multi_timeframe_data(symbol, market_type)
        
        if not data:
            print("   ❌ فشل في جمع البيانات")
            return False
        
        print(f"   ✅ تم جمع بيانات {len(data)} إطار زمني")
        
        # 2. التحليل
        print("   2️⃣ التحليل المتقدم...")
        analyzer = AdvancedMarketAnalyzer()
        analysis = analyzer.analyze_multi_timeframe(data, symbol)
        
        if not analysis:
            print("   ❌ فشل في التحليل")
            return False
        
        print("   ✅ تم إكمال التحليل المتقدم")
        
        # 3. إنشاء التقرير
        print("   3️⃣ إنشاء التقرير...")
        reporter = AdvancedReporter()
        report = reporter.generate_comprehensive_report(symbol, analysis, market_type)
        
        if not report:
            print("   ❌ فشل في إنشاء التقرير")
            return False
        
        print("   ✅ تم إنشاء التقرير الشامل")
        
        # 4. النتائج النهائية
        print("\n🎯 النتائج النهائية:")
        exec_summary = report.get('executive_summary', {})
        print(f"   📈 التوصية: {exec_summary.get('main_recommendation', 'غير محدد')}")
        print(f"   📊 الثقة: {exec_summary.get('confidence_level', 0):.1%}")
        print(f"   ⚠️ المخاطر: {exec_summary.get('risk_level', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في سير العمل: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🚀 اختبار شامل للنظام المتقدم")
    print("=" * 60)
    
    start_time = time.time()
    
    # قائمة الاختبارات
    tests = [
        ("استيراد الوحدات", test_imports),
        ("جمع البيانات المتقدم", test_data_collection),
        ("نظام التحليل المتقدم", test_analysis_system),
        ("نظام التقارير", test_reporting_system),
        ("النماذج المتقدمة", test_advanced_models),
        ("سير العمل الكامل", test_complete_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"💥 {test_name}: خطأ غير متوقع - {str(e)}")
            results.append((test_name, False))
    
    # النتائج النهائية
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{test_name:.<40} {status}")
    
    print("-" * 60)
    print(f"المجموع: {passed}/{total} اختبار نجح")
    print(f"النسبة: {passed/total*100:.1f}%")
    print(f"الوقت المستغرق: {duration:.1f} ثانية")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print(f"\n⚠️ {total-passed} اختبار فشل. يرجى مراجعة الأخطاء أعلاه.")
    
    print("\n💡 لتشغيل التطبيق المتقدم:")
    print("   python -m streamlit run advanced_app.py")
    print("   أو انقر مرتين على run_advanced_app.bat")

if __name__ == "__main__":
    main()

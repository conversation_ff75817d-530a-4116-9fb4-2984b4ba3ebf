"""
نظام التوصيات الذكية المتقدم
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from super_ai_config import SMART_RECOMMENDATIONS_CONFIG, ERROR_LEARNING_CONFIG
from risk_management import RiskAssessment, PositionSizing, TimingOptimizer

class SmartRecommendationEngine:
    """
    محرك التوصيات الذكية المتقدم
    """
    def __init__(self, symbol, market_type='stocks'):
        self.symbol = symbol
        self.market_type = market_type
        self.config = SMART_RECOMMENDATIONS_CONFIG
        self.error_config = ERROR_LEARNING_CONFIG

        # نظام التعلم من الأخطاء
        self.mistake_memory = []
        self.performance_tracker = {}
        self.confidence_calibrator = ConfidenceCalibrator()

        # نماذج التوصيات
        self.signal_classifier = None
        self.risk_assessor = RiskAssessment()
        self.position_sizer = PositionSizing()
        self.timing_optimizer = TimingOptimizer()

        print("🧠 تهيئة محرك التوصيات الذكية...")
        self.initialize_recommendation_system()

    def initialize_recommendation_system(self):
        """
        تهيئة نظام التوصيات
        """
        try:
            # تهيئة مصنف الإشارات
            self.signal_classifier = RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                random_state=42
            )

            print("✅ تم تهيئة محرك التوصيات الذكية")

        except Exception as e:
            print(f"❌ خطأ في تهيئة محرك التوصيات: {str(e)}")

    def generate_smart_recommendation(self, analysis_data, market_data):
        """
        إنتاج توصية ذكية شاملة
        """
        try:
            print("🧠 إنتاج التوصية الذكية...")

            # تحليل الإشارات
            signals = self.analyze_signals(analysis_data)

            # تقييم المخاطر
            risk_assessment = self.risk_assessor.assess_risk(market_data, analysis_data)

            # تحديد حجم المركز
            position_size = self.position_sizer.calculate_optimal_size(
                risk_assessment, self.config['position_sizing']
            )

            # تحسين التوقيت
            timing = self.timing_optimizer.optimize_entry_exit(
                market_data, signals, analysis_data
            )

            # حساب مستوى الثقة
            confidence = self.confidence_calibrator.calculate_confidence(
                signals, risk_assessment, analysis_data
            )

            # التعلم من الأخطاء السابقة
            adjusted_recommendation = self.learn_from_mistakes(
                signals, risk_assessment, confidence
            )

            # إنشاء التوصية النهائية
            recommendation = self.create_final_recommendation(
                adjusted_recommendation, position_size, timing, confidence, risk_assessment
            )

            # حفظ التوصية للتعلم المستقبلي
            self.save_recommendation_for_learning(recommendation, market_data)

            return recommendation

        except Exception as e:
            print(f"❌ خطأ في إنتاج التوصية: {str(e)}")
            return self.create_default_recommendation()

    def analyze_signals(self, analysis_data):
        """
        تحليل الإشارات من جميع المصادر
        """
        try:
            signals = {
                'technical_signals': [],
                'fundamental_signals': [],
                'sentiment_signals': [],
                'pattern_signals': [],
                'overall_strength': 0,
                'direction': 'محايد'
            }

            # إشارات فنية
            if 'technical' in analysis_data:
                technical = analysis_data['technical']
                signals['technical_signals'] = self.extract_technical_signals(technical)

            # إشارات المشاعر
            if 'sentiment' in analysis_data:
                sentiment = analysis_data['sentiment']
                signals['sentiment_signals'] = self.extract_sentiment_signals(sentiment)

            # إشارات الأنماط
            if 'patterns' in analysis_data:
                patterns = analysis_data['patterns']
                signals['pattern_signals'] = self.extract_pattern_signals(patterns)

            # حساب القوة الإجمالية
            signals['overall_strength'] = self.calculate_signal_strength(signals)
            signals['direction'] = self.determine_direction(signals)

            return signals

        except Exception as e:
            return {'error': str(e)}

    def extract_technical_signals(self, technical_data):
        """
        استخراج الإشارات الفنية
        """
        signals = []

        try:
            # إشارات الفراكتال
            if 'fractal' in technical_data:
                fractal = technical_data['fractal']
                if fractal.get('complexity') == 'بسيط':
                    signals.append({
                        'type': 'fractal',
                        'signal': 'اتجاهي قوي',
                        'strength': 0.8,
                        'direction': 'إيجابي'
                    })

            # إشارات موجات إليوت
            if 'elliott_wave' in technical_data:
                elliott = technical_data['elliott_wave']
                if 'الموجة 3' in elliott.get('current_wave', ''):
                    signals.append({
                        'type': 'elliott_wave',
                        'signal': 'موجة دافعة قوية',
                        'strength': 0.9,
                        'direction': 'إيجابي'
                    })

            # إشارات ويكوف
            if 'wyckoff' in technical_data:
                wyckoff = technical_data['wyckoff']
                if wyckoff.get('current_phase') == 'تراكم':
                    signals.append({
                        'type': 'wyckoff',
                        'signal': 'مرحلة تراكم',
                        'strength': 0.7,
                        'direction': 'إيجابي'
                    })

            return signals

        except Exception as e:
            return []

    def extract_sentiment_signals(self, sentiment_data):
        """
        استخراج إشارات المشاعر
        """
        signals = []

        try:
            combined_sentiment = sentiment_data.get('combined_sentiment', 0.5)

            if combined_sentiment > 0.7:
                signals.append({
                    'type': 'sentiment',
                    'signal': 'مشاعر إيجابية قوية',
                    'strength': combined_sentiment,
                    'direction': 'إيجابي'
                })
            elif combined_sentiment < 0.3:
                signals.append({
                    'type': 'sentiment',
                    'signal': 'مشاعر سلبية قوية',
                    'strength': 1 - combined_sentiment,
                    'direction': 'سلبي'
                })

            return signals

        except Exception as e:
            return []

    def extract_pattern_signals(self, pattern_data):
        """
        استخراج إشارات الأنماط
        """
        signals = []

        try:
            # أنماط الشموع المتقدمة
            if 'advanced_candlesticks' in pattern_data:
                candlesticks = pattern_data['advanced_candlesticks']
                for pattern, detected in candlesticks.items():
                    if detected and 'bullish' in pattern.lower():
                        signals.append({
                            'type': 'candlestick',
                            'signal': f'نمط {pattern}',
                            'strength': 0.6,
                            'direction': 'إيجابي'
                        })

            return signals

        except Exception as e:
            return []

    def calculate_signal_strength(self, signals):
        """
        حساب قوة الإشارة الإجمالية
        """
        try:
            total_strength = 0
            signal_count = 0

            for signal_type in ['technical_signals', 'sentiment_signals', 'pattern_signals']:
                for signal in signals.get(signal_type, []):
                    total_strength += signal.get('strength', 0)
                    signal_count += 1

            if signal_count > 0:
                return total_strength / signal_count
            else:
                return 0.5

        except Exception as e:
            return 0.5

    def determine_direction(self, signals):
        """
        تحديد الاتجاه الإجمالي
        """
        try:
            positive_signals = 0
            negative_signals = 0

            for signal_type in ['technical_signals', 'sentiment_signals', 'pattern_signals']:
                for signal in signals.get(signal_type, []):
                    direction = signal.get('direction', 'محايد')
                    strength = signal.get('strength', 0)

                    if direction == 'إيجابي':
                        positive_signals += strength
                    elif direction == 'سلبي':
                        negative_signals += strength

            if positive_signals > negative_signals * 1.2:
                return 'صاعد'
            elif negative_signals > positive_signals * 1.2:
                return 'هابط'
            else:
                return 'جانبي'

        except Exception as e:
            return 'محايد'

    def learn_from_mistakes(self, signals, risk_assessment, confidence):
        """
        التعلم من الأخطاء السابقة
        """
        try:
            # مراجعة الأخطاء السابقة
            similar_mistakes = self.find_similar_mistakes(signals, risk_assessment)

            # تعديل التوصية بناءً على الأخطاء
            adjusted_signals = signals.copy()

            for mistake in similar_mistakes:
                # تقليل الثقة إذا كان هناك أخطاء مشابهة
                if mistake['mistake_type'] == 'false_signal':
                    adjusted_signals['overall_strength'] *= 0.8
                elif mistake['mistake_type'] == 'timing_error':
                    # تأخير التوصية قليلاً
                    pass
                elif mistake['mistake_type'] == 'risk_underestimation':
                    # زيادة تقدير المخاطر
                    risk_assessment['overall_risk'] = min(1.0, risk_assessment.get('overall_risk', 0.5) * 1.2)

            return adjusted_signals

        except Exception as e:
            return signals

    def find_similar_mistakes(self, current_signals, current_risk):
        """
        العثور على أخطاء مشابهة في الماضي
        """
        try:
            similar_mistakes = []

            for mistake in self.mistake_memory:
                # مقارنة الظروف
                similarity_score = self.calculate_similarity(
                    mistake['conditions'],
                    {'signals': current_signals, 'risk': current_risk}
                )

                if similarity_score > 0.7:
                    similar_mistakes.append(mistake)

            return similar_mistakes

        except Exception as e:
            return []

    def calculate_similarity(self, past_conditions, current_conditions):
        """
        حساب التشابه بين الظروف
        """
        try:
            # مقارنة بسيطة للتشابه
            past_direction = past_conditions.get('signals', {}).get('direction', 'محايد')
            current_direction = current_conditions.get('signals', {}).get('direction', 'محايد')

            if past_direction == current_direction:
                return 0.8
            else:
                return 0.3

        except Exception as e:
            return 0.0

    def create_final_recommendation(self, signals, position_size, timing, confidence, risk_assessment):
        """
        إنشاء التوصية النهائية
        """
        try:
            # تحديد نوع التوصية
            direction = signals.get('direction', 'محايد')
            strength = signals.get('overall_strength', 0.5)

            if direction == 'صاعد' and strength > 0.7 and confidence > 0.75:
                action = 'شراء قوي'
                action_type = 'BUY_STRONG'
            elif direction == 'صاعد' and strength > 0.6:
                action = 'شراء'
                action_type = 'BUY'
            elif direction == 'هابط' and strength > 0.7 and confidence > 0.75:
                action = 'بيع قوي'
                action_type = 'SELL_STRONG'
            elif direction == 'هابط' and strength > 0.6:
                action = 'بيع'
                action_type = 'SELL'
            else:
                action = 'انتظار'
                action_type = 'HOLD'

            # حساب الأهداف ووقف الخسارة
            targets = self.calculate_targets(signals, risk_assessment)
            stop_loss = self.calculate_stop_loss(signals, risk_assessment)

            recommendation = {
                'symbol': self.symbol,
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'action_type': action_type,
                'direction': direction,
                'confidence': confidence,
                'strength': strength,
                'position_size': position_size,
                'risk_level': risk_assessment.get('overall_risk', 0.5),
                'entry_timing': timing.get('optimal_entry', 'فوري'),
                'exit_timing': timing.get('optimal_exit', 'حسب الأهداف'),
                'targets': targets,
                'stop_loss': stop_loss,
                'reasoning': self.generate_reasoning(signals, risk_assessment),
                'time_horizon': self.determine_time_horizon(signals, timing),
                'market_conditions': self.assess_market_conditions(risk_assessment)
            }

            return recommendation

        except Exception as e:
            return self.create_default_recommendation()

    def calculate_targets(self, signals, risk_assessment):
        """
        حساب الأهداف السعرية
        """
        try:
            # أهداف افتراضية بناءً على التقلبات
            volatility = risk_assessment.get('volatility', 0.02)

            targets = {
                'target_1': f"+{volatility * 100:.1f}%",
                'target_2': f"+{volatility * 200:.1f}%",
                'target_3': f"+{volatility * 300:.1f}%"
            }

            return targets

        except Exception as e:
            return {'target_1': '+2%', 'target_2': '+4%', 'target_3': '+6%'}

    def calculate_stop_loss(self, signals, risk_assessment):
        """
        حساب وقف الخسارة
        """
        try:
            volatility = risk_assessment.get('volatility', 0.02)
            risk_level = risk_assessment.get('overall_risk', 0.5)

            # وقف خسارة متكيف مع المخاطر
            stop_loss_percentage = volatility * (1 + risk_level) * 100

            return {
                'percentage': f"-{stop_loss_percentage:.1f}%",
                'type': 'متحرك',
                'reasoning': 'بناءً على التقلبات ومستوى المخاطر'
            }

        except Exception as e:
            return {'percentage': '-3%', 'type': 'ثابت'}

    def generate_reasoning(self, signals, risk_assessment):
        """
        إنتاج التبرير للتوصية
        """
        try:
            reasoning = []

            # تبرير الإشارات الفنية
            technical_signals = signals.get('technical_signals', [])
            if technical_signals:
                reasoning.append(f"الإشارات الفنية تشير إلى {signals.get('direction', 'اتجاه محايد')}")

            # تبرير المشاعر
            sentiment_signals = signals.get('sentiment_signals', [])
            if sentiment_signals:
                reasoning.append("مشاعر السوق تدعم التوصية")

            # تبرير المخاطر
            risk_level = risk_assessment.get('overall_risk', 0.5)
            if risk_level < 0.3:
                reasoning.append("مستوى المخاطر منخفض")
            elif risk_level > 0.7:
                reasoning.append("مستوى المخاطر مرتفع - توخي الحذر")

            return ". ".join(reasoning) if reasoning else "تحليل شامل للظروف الحالية"

        except Exception as e:
            return "تحليل تلقائي"

    def determine_time_horizon(self, signals, timing):
        """
        تحديد الأفق الزمني
        """
        try:
            strength = signals.get('overall_strength', 0.5)

            if strength > 0.8:
                return 'قصير المدى (1-3 أيام)'
            elif strength > 0.6:
                return 'متوسط المدى (1-2 أسابيع)'
            else:
                return 'طويل المدى (شهر أو أكثر)'

        except Exception as e:
            return 'متوسط المدى'

    def assess_market_conditions(self, risk_assessment):
        """
        تقييم ظروف السوق
        """
        try:
            volatility = risk_assessment.get('volatility', 0.02)

            if volatility > 0.05:
                return 'متقلب'
            elif volatility < 0.02:
                return 'هادئ'
            else:
                return 'طبيعي'

        except Exception as e:
            return 'طبيعي'

    def create_default_recommendation(self):
        """
        إنشاء توصية افتراضية في حالة الخطأ
        """
        return {
            'symbol': self.symbol,
            'timestamp': datetime.now().isoformat(),
            'action': 'انتظار',
            'action_type': 'HOLD',
            'direction': 'محايد',
            'confidence': 0.5,
            'strength': 0.5,
            'position_size': {'percentage': '1%'},
            'risk_level': 0.5,
            'reasoning': 'تحليل غير مكتمل - يُنصح بالانتظار',
            'targets': {'target_1': '+2%'},
            'stop_loss': {'percentage': '-2%', 'type': 'ثابت'}
        }

    def save_recommendation_for_learning(self, recommendation, market_data):
        """
        حفظ التوصية للتعلم المستقبلي
        """
        try:
            learning_data = {
                'timestamp': recommendation['timestamp'],
                'recommendation': recommendation,
                'market_conditions': {
                    'price': market_data.get('Close', [])[-1] if 'Close' in market_data else 0,
                    'volume': market_data.get('Volume', [])[-1] if 'Volume' in market_data else 0
                }
            }

            # حفظ في ذاكرة التعلم
            if len(self.performance_tracker) > 1000:
                # إزالة البيانات القديمة
                oldest_key = min(self.performance_tracker.keys())
                del self.performance_tracker[oldest_key]

            self.performance_tracker[recommendation['timestamp']] = learning_data

        except Exception as e:
            print(f"خطأ في حفظ بيانات التعلم: {str(e)}")

class ConfidenceCalibrator:
    """
    معايرة مستوى الثقة
    """
    def __init__(self):
        self.historical_accuracy = {}

    def calculate_confidence(self, signals, risk_assessment, analysis_data):
        """
        حساب مستوى الثقة
        """
        try:
            base_confidence = signals.get('overall_strength', 0.5)

            # تعديل بناءً على المخاطر
            risk_factor = 1 - risk_assessment.get('overall_risk', 0.5) * 0.3

            # تعديل بناءً على جودة البيانات
            data_quality = self.assess_data_quality(analysis_data)

            final_confidence = base_confidence * risk_factor * data_quality

            return max(0.1, min(0.95, final_confidence))

        except Exception as e:
            return 0.5

    def assess_data_quality(self, analysis_data):
        """
        تقييم جودة البيانات
        """
        try:
            quality_score = 0.5

            # فحص توفر البيانات المختلفة
            if 'technical' in analysis_data:
                quality_score += 0.2
            if 'sentiment' in analysis_data:
                quality_score += 0.2
            if 'patterns' in analysis_data:
                quality_score += 0.1

            return min(1.0, quality_score)

        except Exception as e:
            return 0.5

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء محرك التوصيات
    engine = SmartRecommendationEngine('AAPL', 'stocks')

    print("🧠 محرك التوصيات الذكية جاهز للعمل!")

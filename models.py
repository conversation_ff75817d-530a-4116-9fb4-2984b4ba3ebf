"""
نماذج التعلم الآلي للتنبؤ بالأسهم
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
# TensorFlow imports commented out for compatibility
# import tensorflow as tf
# from tensorflow.keras.models import Sequential
# from tensorflow.keras.layers import LSTM, Dense, Dropout
# from tensorflow.keras.optimizers import Adam
# from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import joblib
import os
from config import MODEL_CONFIG, PATHS

class LSTMModel:
    """
    نموذج LSTM معطل مؤقتاً بسبب عدم توفر TensorFlow
    """
    def __init__(self, input_shape):
        self.input_shape = input_shape
        self.model = None
        self.config = MODEL_CONFIG['lstm']
        print("تحذير: نموذج LSTM غير متاح - يتطلب TensorFlow")

    def build_model(self):
        print("نموذج LSTM غير متاح - يتطلب TensorFlow")
        return None

    def train(self, X_train, y_train, X_val, y_val):
        print("نموذج LSTM غير متاح - يتطلب TensorFlow")
        return None

    def predict(self, X):
        print("نموذج LSTM غير متاح - يتطلب TensorFlow")
        return None

    def save_model(self, symbol):
        print("نموذج LSTM غير متاح - يتطلب TensorFlow")

    def load_model(self, symbol):
        print("نموذج LSTM غير متاح - يتطلب TensorFlow")
        return False

class RandomForestModel:
    def __init__(self):
        self.config = MODEL_CONFIG['random_forest']
        self.model = RandomForestRegressor(**self.config)

    def train(self, X_train, y_train):
        """
        تدريب نموذج Random Forest
        """
        try:
            self.model.fit(X_train, y_train)
            return True

        except Exception as e:
            print(f"خطأ في تدريب Random Forest: {str(e)}")
            return False

    def predict(self, X):
        """
        التنبؤ باستخدام Random Forest
        """
        try:
            predictions = self.model.predict(X)
            return predictions

        except Exception as e:
            print(f"خطأ في التنبؤ بـ Random Forest: {str(e)}")
            return None

    def get_feature_importance(self):
        """
        الحصول على أهمية الميزات
        """
        try:
            return self.model.feature_importances_
        except Exception as e:
            print(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return None

    def save_model(self, symbol):
        """
        حفظ نموذج Random Forest
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_rf_model.pkl')
            joblib.dump(self.model, model_path)
            print(f"تم حفظ نموذج Random Forest لـ {symbol}")

        except Exception as e:
            print(f"خطأ في حفظ نموذج Random Forest: {str(e)}")

    def load_model(self, symbol):
        """
        تحميل نموذج Random Forest
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_rf_model.pkl')

            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                return True
            else:
                print(f"نموذج Random Forest غير موجود لـ {symbol}")
                return False

        except Exception as e:
            print(f"خطأ في تحميل نموذج Random Forest: {str(e)}")
            return False

class XGBoostModel:
    def __init__(self):
        self.config = MODEL_CONFIG['xgboost']
        self.model = xgb.XGBRegressor(**self.config)

    def train(self, X_train, y_train, X_val=None, y_val=None):
        """
        تدريب نموذج XGBoost
        """
        try:
            if X_val is not None and y_val is not None:
                eval_set = [(X_val, y_val)]
                self.model.fit(
                    X_train, y_train,
                    eval_set=eval_set,
                    early_stopping_rounds=10,
                    verbose=False
                )
            else:
                self.model.fit(X_train, y_train)

            return True

        except Exception as e:
            print(f"خطأ في تدريب XGBoost: {str(e)}")
            return False

    def predict(self, X):
        """
        التنبؤ باستخدام XGBoost
        """
        try:
            predictions = self.model.predict(X)
            return predictions

        except Exception as e:
            print(f"خطأ في التنبؤ بـ XGBoost: {str(e)}")
            return None

    def get_feature_importance(self):
        """
        الحصول على أهمية الميزات
        """
        try:
            return self.model.feature_importances_
        except Exception as e:
            print(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return None

    def save_model(self, symbol):
        """
        حفظ نموذج XGBoost
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_xgb_model.pkl')
            joblib.dump(self.model, model_path)
            print(f"تم حفظ نموذج XGBoost لـ {symbol}")

        except Exception as e:
            print(f"خطأ في حفظ نموذج XGBoost: {str(e)}")

    def load_model(self, symbol):
        """
        تحميل نموذج XGBoost
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_xgb_model.pkl')

            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                return True
            else:
                print(f"نموذج XGBoost غير موجود لـ {symbol}")
                return False

        except Exception as e:
            print(f"خطأ في تحميل نموذج XGBoost: {str(e)}")
            return False

def evaluate_model(y_true, y_pred, model_name="Model"):
    """
    تقييم أداء النموذج
    """
    try:
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)

        # حساب MAPE
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

        metrics = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2,
            'MAPE': mape
        }

        print(f"\n=== تقييم {model_name} ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")

        return metrics

    except Exception as e:
        print(f"خطأ في تقييم النموذج: {str(e)}")
        return None

class EnsembleModel:
    """
    نموذج مجمع يجمع تنبؤات عدة نماذج
    """
    def __init__(self):
        self.models = {}
        self.weights = {}

    def add_model(self, name, model, weight=1.0):
        """
        إضافة نموذج للمجموعة
        """
        self.models[name] = model
        self.weights[name] = weight

    def predict(self, X_lstm=None, X_ml=None):
        """
        التنبؤ المجمع
        """
        try:
            predictions = {}

            # تنبؤات LSTM
            if 'lstm' in self.models and X_lstm is not None:
                lstm_pred = self.models['lstm'].predict(X_lstm)
                if lstm_pred is not None:
                    predictions['lstm'] = lstm_pred

            # تنبؤات Random Forest
            if 'rf' in self.models and X_ml is not None:
                rf_pred = self.models['rf'].predict(X_ml)
                if rf_pred is not None:
                    predictions['rf'] = rf_pred

            # تنبؤات XGBoost
            if 'xgb' in self.models and X_ml is not None:
                xgb_pred = self.models['xgb'].predict(X_ml)
                if xgb_pred is not None:
                    predictions['xgb'] = xgb_pred

            if not predictions:
                return None

            # حساب المتوسط المرجح
            total_weight = 0
            weighted_sum = 0

            for name, pred in predictions.items():
                weight = self.weights.get(name, 1.0)
                if len(pred) > 0:
                    weighted_sum += weight * pred[-1]  # آخر تنبؤ
                    total_weight += weight

            if total_weight > 0:
                ensemble_prediction = weighted_sum / total_weight
                return ensemble_prediction
            else:
                return None

        except Exception as e:
            print(f"خطأ في التنبؤ المجمع: {str(e)}")
            return None

"""
نماذج التعلم الآلي للتنبؤ بالأسهم
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
# استخدام PyTorch للشبكات العصبية
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    print("تحذير: PyTorch غير متاح")
import joblib
import os
from config import MODEL_CONFIG, PATHS

# نموذج LSTM باستخدام PyTorch
class PyTorchLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, dropout=0.2):
        super(PyTorchLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, 1)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out

class LSTMModel:
    def __init__(self, input_shape):
        self.input_shape = input_shape
        self.model = None
        self.config = MODEL_CONFIG['lstm']
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        if not PYTORCH_AVAILABLE:
            print("تحذير: نموذج LSTM غير متاح - يتطلب PyTorch")

    def build_model(self):
        """
        بناء نموذج LSTM
        """
        if not PYTORCH_AVAILABLE:
            print("نموذج LSTM غير متاح - يتطلب PyTorch")
            return None

        try:
            input_size = self.input_shape[1]  # عدد الميزات
            hidden_size = self.config['units'][0]
            num_layers = len(self.config['units'])
            dropout = self.config['dropout']

            self.model = PyTorchLSTM(input_size, hidden_size, num_layers, dropout)
            self.model.to(self.device)

            return self.model

        except Exception as e:
            print(f"خطأ في بناء نموذج LSTM: {str(e)}")
            return None

    def train(self, X_train, y_train, X_val, y_val):
        """
        تدريب نموذج LSTM
        """
        if not PYTORCH_AVAILABLE:
            print("نموذج LSTM غير متاح - يتطلب PyTorch")
            return None

        try:
            if self.model is None:
                self.build_model()

            if self.model is None:
                return None

            # تحويل البيانات إلى tensors
            X_train_tensor = torch.FloatTensor(X_train).to(self.device)
            y_train_tensor = torch.FloatTensor(y_train).to(self.device)
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)

            # إعداد optimizer و loss function
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            criterion = nn.MSELoss()

            # التدريب
            epochs = min(self.config['epochs'], 50)  # تقليل العدد للاختبار
            batch_size = self.config['batch_size']

            train_losses = []
            val_losses = []

            for epoch in range(epochs):
                self.model.train()
                total_loss = 0

                # تدريب بـ batches
                for i in range(0, len(X_train_tensor), batch_size):
                    batch_X = X_train_tensor[i:i+batch_size]
                    batch_y = y_train_tensor[i:i+batch_size]

                    optimizer.zero_grad()
                    outputs = self.model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    optimizer.step()

                    total_loss += loss.item()

                # التحقق من الصحة
                self.model.eval()
                with torch.no_grad():
                    val_outputs = self.model(X_val_tensor)
                    val_loss = criterion(val_outputs.squeeze(), y_val_tensor)

                avg_train_loss = total_loss / (len(X_train_tensor) // batch_size + 1)
                train_losses.append(avg_train_loss)
                val_losses.append(val_loss.item())

                if epoch % 10 == 0:
                    print(f'Epoch {epoch}/{epochs}, Train Loss: {avg_train_loss:.4f}, Val Loss: {val_loss.item():.4f}')

            return {'train_losses': train_losses, 'val_losses': val_losses}

        except Exception as e:
            print(f"خطأ في تدريب LSTM: {str(e)}")
            return None

    def predict(self, X):
        """
        التنبؤ باستخدام LSTM
        """
        if not PYTORCH_AVAILABLE:
            print("نموذج LSTM غير متاح - يتطلب PyTorch")
            return None

        try:
            if self.model is None:
                print("النموذج غير مدرب")
                return None

            self.model.eval()
            with torch.no_grad():
                X_tensor = torch.FloatTensor(X).to(self.device)
                predictions = self.model(X_tensor)
                return predictions.cpu().numpy().flatten()

        except Exception as e:
            print(f"خطأ في التنبؤ بـ LSTM: {str(e)}")
            return None

    def save_model(self, symbol):
        """
        حفظ نموذج LSTM
        """
        if not PYTORCH_AVAILABLE or self.model is None:
            print("نموذج LSTM غير متاح للحفظ")
            return

        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_lstm_model.pth')
            torch.save(self.model.state_dict(), model_path)

            # حفظ معلومات النموذج
            model_info = {
                'input_shape': self.input_shape,
                'config': self.config
            }
            info_path = os.path.join(PATHS['models'], f'{symbol}_lstm_info.pkl')
            joblib.dump(model_info, info_path)

            print(f"تم حفظ نموذج LSTM لـ {symbol}")

        except Exception as e:
            print(f"خطأ في حفظ نموذج LSTM: {str(e)}")

    def load_model(self, symbol):
        """
        تحميل نموذج LSTM
        """
        if not PYTORCH_AVAILABLE:
            print("نموذج LSTM غير متاح - يتطلب PyTorch")
            return False

        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_lstm_model.pth')
            info_path = os.path.join(PATHS['models'], f'{symbol}_lstm_info.pkl')

            if os.path.exists(model_path) and os.path.exists(info_path):
                # تحميل معلومات النموذج
                model_info = joblib.load(info_path)
                self.input_shape = model_info['input_shape']
                self.config = model_info['config']

                # بناء النموذج
                self.build_model()

                # تحميل الأوزان
                self.model.load_state_dict(torch.load(model_path, map_location=self.device))
                self.model.eval()

                return True
            else:
                print(f"نموذج LSTM غير موجود لـ {symbol}")
                return False

        except Exception as e:
            print(f"خطأ في تحميل نموذج LSTM: {str(e)}")
            return False

class RandomForestModel:
    def __init__(self):
        self.config = MODEL_CONFIG['random_forest']
        self.model = RandomForestRegressor(**self.config)

    def train(self, X_train, y_train):
        """
        تدريب نموذج Random Forest
        """
        try:
            self.model.fit(X_train, y_train)
            return True

        except Exception as e:
            print(f"خطأ في تدريب Random Forest: {str(e)}")
            return False

    def predict(self, X):
        """
        التنبؤ باستخدام Random Forest
        """
        try:
            predictions = self.model.predict(X)
            return predictions

        except Exception as e:
            print(f"خطأ في التنبؤ بـ Random Forest: {str(e)}")
            return None

    def get_feature_importance(self):
        """
        الحصول على أهمية الميزات
        """
        try:
            return self.model.feature_importances_
        except Exception as e:
            print(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return None

    def save_model(self, symbol):
        """
        حفظ نموذج Random Forest
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_rf_model.pkl')
            joblib.dump(self.model, model_path)
            print(f"تم حفظ نموذج Random Forest لـ {symbol}")

        except Exception as e:
            print(f"خطأ في حفظ نموذج Random Forest: {str(e)}")

    def load_model(self, symbol):
        """
        تحميل نموذج Random Forest
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_rf_model.pkl')

            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                return True
            else:
                print(f"نموذج Random Forest غير موجود لـ {symbol}")
                return False

        except Exception as e:
            print(f"خطأ في تحميل نموذج Random Forest: {str(e)}")
            return False

class XGBoostModel:
    def __init__(self):
        self.config = MODEL_CONFIG['xgboost']
        self.model = xgb.XGBRegressor(**self.config)

    def train(self, X_train, y_train, X_val=None, y_val=None):
        """
        تدريب نموذج XGBoost
        """
        try:
            if X_val is not None and y_val is not None:
                eval_set = [(X_val, y_val)]
                self.model.fit(
                    X_train, y_train,
                    eval_set=eval_set,
                    early_stopping_rounds=10,
                    verbose=False
                )
            else:
                self.model.fit(X_train, y_train)

            return True

        except Exception as e:
            print(f"خطأ في تدريب XGBoost: {str(e)}")
            return False

    def predict(self, X):
        """
        التنبؤ باستخدام XGBoost
        """
        try:
            predictions = self.model.predict(X)
            return predictions

        except Exception as e:
            print(f"خطأ في التنبؤ بـ XGBoost: {str(e)}")
            return None

    def get_feature_importance(self):
        """
        الحصول على أهمية الميزات
        """
        try:
            return self.model.feature_importances_
        except Exception as e:
            print(f"خطأ في الحصول على أهمية الميزات: {str(e)}")
            return None

    def save_model(self, symbol):
        """
        حفظ نموذج XGBoost
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_xgb_model.pkl')
            joblib.dump(self.model, model_path)
            print(f"تم حفظ نموذج XGBoost لـ {symbol}")

        except Exception as e:
            print(f"خطأ في حفظ نموذج XGBoost: {str(e)}")

    def load_model(self, symbol):
        """
        تحميل نموذج XGBoost
        """
        try:
            model_path = os.path.join(PATHS['models'], f'{symbol}_xgb_model.pkl')

            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                return True
            else:
                print(f"نموذج XGBoost غير موجود لـ {symbol}")
                return False

        except Exception as e:
            print(f"خطأ في تحميل نموذج XGBoost: {str(e)}")
            return False

def evaluate_model(y_true, y_pred, model_name="Model"):
    """
    تقييم أداء النموذج
    """
    try:
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)

        # حساب MAPE
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100

        metrics = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2,
            'MAPE': mape
        }

        print(f"\n=== تقييم {model_name} ===")
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")

        return metrics

    except Exception as e:
        print(f"خطأ في تقييم النموذج: {str(e)}")
        return None

class EnsembleModel:
    """
    نموذج مجمع يجمع تنبؤات عدة نماذج
    """
    def __init__(self):
        self.models = {}
        self.weights = {}

    def add_model(self, name, model, weight=1.0):
        """
        إضافة نموذج للمجموعة
        """
        self.models[name] = model
        self.weights[name] = weight

    def predict(self, X_lstm=None, X_ml=None):
        """
        التنبؤ المجمع
        """
        try:
            predictions = {}

            # تنبؤات LSTM
            if 'lstm' in self.models and X_lstm is not None:
                lstm_pred = self.models['lstm'].predict(X_lstm)
                if lstm_pred is not None:
                    predictions['lstm'] = lstm_pred

            # تنبؤات Random Forest
            if 'rf' in self.models and X_ml is not None:
                rf_pred = self.models['rf'].predict(X_ml)
                if rf_pred is not None:
                    predictions['rf'] = rf_pred

            # تنبؤات XGBoost
            if 'xgb' in self.models and X_ml is not None:
                xgb_pred = self.models['xgb'].predict(X_ml)
                if xgb_pred is not None:
                    predictions['xgb'] = xgb_pred

            if not predictions:
                return None

            # حساب المتوسط المرجح
            total_weight = 0
            weighted_sum = 0

            for name, pred in predictions.items():
                weight = self.weights.get(name, 1.0)
                if len(pred) > 0:
                    weighted_sum += weight * pred[-1]  # آخر تنبؤ
                    total_weight += weight

            if total_weight > 0:
                ensemble_prediction = weighted_sum / total_weight
                return ensemble_prediction
            else:
                return None

        except Exception as e:
            print(f"خطأ في التنبؤ المجمع: {str(e)}")
            return None

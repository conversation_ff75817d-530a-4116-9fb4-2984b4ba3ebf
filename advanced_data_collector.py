"""
جامع البيانات المتقدم للأسواق المتعددة
"""

import yfinance as yf
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import ta
import os
import json
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from advanced_config import MARKETS_CONFIG, ANALYSIS_CONFIG, ADVANCED_PATHS, NEWS_CONFIG

class AdvancedDataCollector:
    def __init__(self):
        self.markets = MARKETS_CONFIG
        self.cache_dir = ADVANCED_PATHS['market_data']
        self.news_dir = ADVANCED_PATHS['news_data']
        
    def fetch_multi_timeframe_data(self, symbol, market_type='stocks'):
        """
        جلب البيانات لعدة إطارات زمنية
        """
        try:
            timeframes_data = {}
            
            for timeframe, config in ANALYSIS_CONFIG['timeframes'].items():
                print(f"جاري جلب بيانات {timeframe} لـ {symbol}...")
                
                ticker = yf.Ticker(symbol)
                data = ticker.history(
                    period=config['period'],
                    interval=config['interval']
                )
                
                if not data.empty:
                    # إضافة المؤشرات الفنية
                    data = self.add_advanced_indicators(data)
                    timeframes_data[timeframe] = data
                    
                    # حفظ في الكاش
                    self.save_to_cache(symbol, timeframe, data)
                
                time.sleep(0.1)  # تجنب تجاوز حدود API
            
            return timeframes_data
            
        except Exception as e:
            print(f"خطأ في جلب البيانات متعددة الإطارات لـ {symbol}: {str(e)}")
            return {}
    
    def add_advanced_indicators(self, data):
        """
        إضافة مؤشرات فنية متقدمة
        """
        try:
            # المتوسطات المتحركة المتقدمة
            for period in [10, 20, 50, 200]:
                data[f'SMA_{period}'] = ta.trend.sma_indicator(data['Close'], window=period)
                data[f'EMA_{period}'] = ta.trend.ema_indicator(data['Close'], window=period)
            
            # مؤشرات الزخم المتقدمة
            data['RSI'] = ta.momentum.rsi(data['Close'], window=14)
            data['RSI_30'] = ta.momentum.rsi(data['Close'], window=30)
            
            # MACD متقدم
            macd = ta.trend.MACD(data['Close'])
            data['MACD'] = macd.macd()
            data['MACD_signal'] = macd.macd_signal()
            data['MACD_histogram'] = macd.macd_diff()
            
            # Bollinger Bands متقدمة
            bb = ta.volatility.BollingerBands(data['Close'])
            data['BB_upper'] = bb.bollinger_hband()
            data['BB_lower'] = bb.bollinger_lband()
            data['BB_middle'] = bb.bollinger_mavg()
            data['BB_width'] = (data['BB_upper'] - data['BB_lower']) / data['BB_middle']
            data['BB_position'] = (data['Close'] - data['BB_lower']) / (data['BB_upper'] - data['BB_lower'])
            
            # مؤشرات التقلبات
            data['ATR'] = ta.volatility.average_true_range(data['High'], data['Low'], data['Close'])
            data['Volatility'] = data['Close'].pct_change().rolling(window=20).std() * np.sqrt(252)
            
            # مؤشرات الحجم المتقدمة
            data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            data['Volume_Ratio'] = data['Volume'] / data['Volume_SMA']
            data['OBV'] = ta.volume.on_balance_volume(data['Close'], data['Volume'])
            data['CMF'] = ta.volume.chaikin_money_flow(data['High'], data['Low'], data['Close'], data['Volume'])
            
            # مؤشرات الدعم والمقاومة
            data = self.calculate_pivot_points(data)
            
            # مؤشرات الاتجاه المتقدمة
            data['ADX'] = ta.trend.adx(data['High'], data['Low'], data['Close'])
            data['CCI'] = ta.trend.cci(data['High'], data['Low'], data['Close'])
            
            # مؤشرات الزخم الإضافية
            data['Williams_R'] = ta.momentum.williams_r(data['High'], data['Low'], data['Close'])
            data['Stoch_K'] = ta.momentum.stoch(data['High'], data['Low'], data['Close'])
            data['Stoch_D'] = ta.momentum.stoch_signal(data['High'], data['Low'], data['Close'])
            
            # مؤشرات السعر والحجم
            data['VWAP'] = ta.volume.volume_weighted_average_price(
                data['High'], data['Low'], data['Close'], data['Volume']
            )
            
            # إضافة ميزات السعر
            data['Price_Change'] = data['Close'].pct_change()
            data['High_Low_Ratio'] = data['High'] / data['Low']
            data['Open_Close_Ratio'] = data['Open'] / data['Close']
            data['Price_Range'] = (data['High'] - data['Low']) / data['Close']
            
            # ميزات الاتجاه
            data['Trend_Strength'] = abs(data['Close'] - data['SMA_20']) / data['SMA_20']
            data['Momentum_5'] = data['Close'] / data['Close'].shift(5) - 1
            data['Momentum_10'] = data['Close'] / data['Close'].shift(10) - 1
            
            return data
            
        except Exception as e:
            print(f"خطأ في إضافة المؤشرات المتقدمة: {str(e)}")
            return data
    
    def calculate_pivot_points(self, data):
        """
        حساب نقاط الدعم والمقاومة
        """
        try:
            # Pivot Point الكلاسيكي
            data['Pivot'] = (data['High'] + data['Low'] + data['Close']) / 3
            data['R1'] = 2 * data['Pivot'] - data['Low']
            data['S1'] = 2 * data['Pivot'] - data['High']
            data['R2'] = data['Pivot'] + (data['High'] - data['Low'])
            data['S2'] = data['Pivot'] - (data['High'] - data['Low'])
            data['R3'] = data['High'] + 2 * (data['Pivot'] - data['Low'])
            data['S3'] = data['Low'] - 2 * (data['High'] - data['Pivot'])
            
            return data
            
        except Exception as e:
            print(f"خطأ في حساب نقاط الدعم والمقاومة: {str(e)}")
            return data
    
    def fetch_market_data_parallel(self, symbols, market_type='stocks'):
        """
        جلب بيانات عدة رموز بشكل متوازي
        """
        all_data = {}
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {
                executor.submit(self.fetch_multi_timeframe_data, symbol, market_type): symbol
                for symbol in symbols
            }
            
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    data = future.result()
                    if data:
                        all_data[symbol] = data
                        print(f"✅ تم جلب بيانات {symbol}")
                except Exception as e:
                    print(f"❌ خطأ في جلب بيانات {symbol}: {str(e)}")
        
        return all_data
    
    def fetch_forex_data(self, symbols=None):
        """
        جلب بيانات العملات
        """
        if symbols is None:
            symbols = self.markets['forex']['symbols']
        
        return self.fetch_market_data_parallel(symbols, 'forex')
    
    def fetch_commodities_data(self, symbols=None):
        """
        جلب بيانات السلع
        """
        if symbols is None:
            symbols = self.markets['commodities']['symbols']
        
        return self.fetch_market_data_parallel(symbols, 'commodities')
    
    def fetch_crypto_data(self, symbols=None):
        """
        جلب بيانات العملات المشفرة
        """
        if symbols is None:
            symbols = self.markets['crypto']['symbols']
        
        return self.fetch_market_data_parallel(symbols, 'crypto')
    
    def fetch_indices_data(self, symbols=None):
        """
        جلب بيانات المؤشرات
        """
        if symbols is None:
            symbols = self.markets['indices']['symbols']
        
        return self.fetch_market_data_parallel(symbols, 'indices')
    
    def save_to_cache(self, symbol, timeframe, data):
        """
        حفظ البيانات في الكاش
        """
        try:
            cache_file = os.path.join(
                self.cache_dir, 
                f"{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d')}.csv"
            )
            data.to_csv(cache_file)
        except Exception as e:
            print(f"خطأ في حفظ الكاش: {str(e)}")
    
    def load_from_cache(self, symbol, timeframe):
        """
        تحميل البيانات من الكاش
        """
        try:
            cache_file = os.path.join(
                self.cache_dir, 
                f"{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d')}.csv"
            )
            
            if os.path.exists(cache_file):
                return pd.read_csv(cache_file, index_col=0, parse_dates=True)
            
        except Exception as e:
            print(f"خطأ في تحميل الكاش: {str(e)}")
        
        return None
    
    def get_market_overview(self):
        """
        الحصول على نظرة عامة على السوق
        """
        try:
            overview = {}
            
            # المؤشرات الرئيسية
            major_indices = ['^GSPC', '^DJI', '^IXIC', '^VIX']
            indices_data = self.fetch_market_data_parallel(major_indices, 'indices')
            overview['indices'] = indices_data
            
            # العملات الرئيسية
            major_forex = ['EURUSD=X', 'GBPUSD=X', 'USDJPY=X']
            forex_data = self.fetch_market_data_parallel(major_forex, 'forex')
            overview['forex'] = forex_data
            
            # السلع الرئيسية
            major_commodities = ['GC=F', 'CL=F']  # الذهب والنفط
            commodities_data = self.fetch_market_data_parallel(major_commodities, 'commodities')
            overview['commodities'] = commodities_data
            
            return overview
            
        except Exception as e:
            print(f"خطأ في الحصول على نظرة عامة: {str(e)}")
            return {}
    
    def detect_market_patterns(self, data):
        """
        اكتشاف أنماط السوق
        """
        try:
            patterns = {}
            
            # نمط الشموع اليابانية
            patterns['candlestick'] = self.detect_candlestick_patterns(data)
            
            # أنماط الرسم البياني
            patterns['chart'] = self.detect_chart_patterns(data)
            
            # أنماط الحجم
            patterns['volume'] = self.detect_volume_patterns(data)
            
            return patterns
            
        except Exception as e:
            print(f"خطأ في اكتشاف الأنماط: {str(e)}")
            return {}
    
    def detect_candlestick_patterns(self, data):
        """
        اكتشاف أنماط الشموع اليابانية
        """
        patterns = {}
        
        try:
            # Doji
            body_size = abs(data['Close'] - data['Open'])
            total_range = data['High'] - data['Low']
            patterns['doji'] = (body_size / total_range) < 0.1
            
            # Hammer
            lower_shadow = data['Open'].combine(data['Close'], min) - data['Low']
            upper_shadow = data['High'] - data['Open'].combine(data['Close'], max)
            patterns['hammer'] = (lower_shadow > 2 * body_size) & (upper_shadow < body_size)
            
            # Shooting Star
            patterns['shooting_star'] = (upper_shadow > 2 * body_size) & (lower_shadow < body_size)
            
            return patterns
            
        except Exception as e:
            print(f"خطأ في اكتشاف أنماط الشموع: {str(e)}")
            return {}
    
    def detect_chart_patterns(self, data):
        """
        اكتشاف أنماط الرسم البياني
        """
        patterns = {}
        
        try:
            # Double Top/Bottom (مبسط)
            highs = data['High'].rolling(window=20).max()
            lows = data['Low'].rolling(window=20).min()
            
            patterns['potential_double_top'] = (data['High'] >= highs * 0.98) & (data['High'].shift(1) < highs * 0.98)
            patterns['potential_double_bottom'] = (data['Low'] <= lows * 1.02) & (data['Low'].shift(1) > lows * 1.02)
            
            return patterns
            
        except Exception as e:
            print(f"خطأ في اكتشاف أنماط الرسم البياني: {str(e)}")
            return {}
    
    def detect_volume_patterns(self, data):
        """
        اكتشاف أنماط الحجم
        """
        patterns = {}
        
        try:
            volume_sma = data['Volume'].rolling(window=20).mean()
            
            patterns['high_volume'] = data['Volume'] > volume_sma * 2
            patterns['low_volume'] = data['Volume'] < volume_sma * 0.5
            patterns['volume_spike'] = data['Volume'] > volume_sma * 3
            
            return patterns
            
        except Exception as e:
            print(f"خطأ في اكتشاف أنماط الحجم: {str(e)}")
            return {}

# مثال على الاستخدام
if __name__ == "__main__":
    collector = AdvancedDataCollector()
    
    # جلب بيانات متعددة الإطارات لسهم Apple
    print("جاري جلب البيانات المتقدمة...")
    aapl_data = collector.fetch_multi_timeframe_data('AAPL')
    
    if aapl_data:
        print("✅ تم جلب البيانات بنجاح!")
        for timeframe, data in aapl_data.items():
            print(f"{timeframe}: {len(data)} صف")
    
    # جلب نظرة عامة على السوق
    print("\nجاري جلب نظرة عامة على السوق...")
    overview = collector.get_market_overview()
    print(f"تم جلب بيانات {len(overview)} فئة من الأسواق")

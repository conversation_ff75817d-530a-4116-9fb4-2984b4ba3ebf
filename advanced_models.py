"""
نماذج الذكاء الاصطناعي المتقدمة مع التعلم المستمر
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from advanced_config import ADVANCED_MODEL_CONFIG, CONTINUOUS_LEARNING_CONFIG, ADVANCED_PATHS

class AdvancedLSTMModel(nn.Module):
    """
    نموذج LSTM متقدم مع طبقات Attention
    """
    def __init__(self, input_size, hidden_size, num_layers, output_size=1, dropout=0.2):
        super(AdvancedLSTMModel, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # طبقات LSTM
        self.lstm = nn.LSTM(
            input_size, hidden_size, num_layers,
            batch_first=True, dropout=dropout, bidirectional=True
        )
        
        # طبقة Attention
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,  # bidirectional
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # طبقات Dense
        self.fc1 = nn.Linear(hidden_size * 2, hidden_size)
        self.dropout = nn.Dropout(dropout)
        self.fc2 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc3 = nn.Linear(hidden_size // 2, output_size)
        
        # طبقات التطبيع
        self.layer_norm1 = nn.LayerNorm(hidden_size * 2)
        self.layer_norm2 = nn.LayerNorm(hidden_size)
        
        # دالة التفعيل
        self.relu = nn.ReLU()
        self.gelu = nn.GELU()
        
    def forward(self, x):
        # LSTM
        lstm_out, _ = self.lstm(x)
        
        # Attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        attn_out = self.layer_norm1(attn_out + lstm_out)  # Residual connection
        
        # أخذ آخر output
        last_output = attn_out[:, -1, :]
        
        # طبقات Dense
        out = self.fc1(last_output)
        out = self.layer_norm2(out)
        out = self.gelu(out)
        out = self.dropout(out)
        
        out = self.fc2(out)
        out = self.relu(out)
        out = self.dropout(out)
        
        out = self.fc3(out)
        
        return out

class TransformerModel(nn.Module):
    """
    نموذج Transformer للتنبؤ بالأسعار
    """
    def __init__(self, input_size, d_model, nhead, num_layers, output_size=1, dropout=0.1):
        super(TransformerModel, self).__init__()
        
        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)
        
        # Positional Encoding
        self.pos_encoding = PositionalEncoding(d_model, dropout)
        
        # Transformer Encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # Output layers
        self.fc = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, output_size)
        )
        
    def forward(self, x):
        # Input projection
        x = self.input_projection(x)
        
        # Positional encoding
        x = self.pos_encoding(x)
        
        # Transformer
        transformer_out = self.transformer(x)
        
        # Global average pooling
        pooled = transformer_out.mean(dim=1)
        
        # Output
        output = self.fc(pooled)
        
        return output

class PositionalEncoding(nn.Module):
    """
    Positional Encoding للـ Transformer
    """
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        x = x + self.pe[:x.size(1), :].transpose(0, 1)
        return self.dropout(x)

class ContinuousLearningSystem:
    """
    نظام التعلم المستمر
    """
    def __init__(self, symbol, market_type='stocks'):
        self.symbol = symbol
        self.market_type = market_type
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # النماذج
        self.models = {}
        self.scalers = {}
        self.performance_history = {}
        
        # إعدادات التعلم المستمر
        self.learning_config = CONTINUOUS_LEARNING_CONFIG
        self.model_config = ADVANCED_MODEL_CONFIG
        
        # مسارات الحفظ
        self.model_dir = os.path.join(ADVANCED_PATHS['models'], symbol)
        os.makedirs(self.model_dir, exist_ok=True)
        
    def initialize_models(self, input_shape):
        """
        تهيئة جميع النماذج
        """
        try:
            print(f"تهيئة النماذج لـ {self.symbol}...")
            
            # LSTM المتقدم
            self.models['advanced_lstm'] = AdvancedLSTMModel(
                input_size=input_shape[2],
                hidden_size=self.model_config['lstm_advanced']['units'][0],
                num_layers=len(self.model_config['lstm_advanced']['units']),
                dropout=self.model_config['lstm_advanced']['dropout']
            ).to(self.device)
            
            # Transformer
            self.models['transformer'] = TransformerModel(
                input_size=input_shape[2],
                d_model=self.model_config['transformer']['d_model'],
                nhead=self.model_config['transformer']['nhead'],
                num_layers=self.model_config['transformer']['num_layers'],
                dropout=self.model_config['transformer']['dropout']
            ).to(self.device)
            
            # Random Forest المحسن
            self.models['enhanced_rf'] = RandomForestRegressor(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1
            )
            
            # Gradient Boosting
            self.models['gradient_boost'] = GradientBoostingRegressor(
                n_estimators=150,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            )
            
            # إعداد المحسنات للنماذج العميقة
            self.optimizers = {
                'advanced_lstm': optim.AdamW(
                    self.models['advanced_lstm'].parameters(),
                    lr=self.model_config['lstm_advanced']['learning_rate'],
                    weight_decay=1e-5
                ),
                'transformer': optim.AdamW(
                    self.models['transformer'].parameters(),
                    lr=0.0001,
                    weight_decay=1e-5
                )
            }
            
            # إعداد المخططات
            self.schedulers = {
                'advanced_lstm': optim.lr_scheduler.ReduceLROnPlateau(
                    self.optimizers['advanced_lstm'],
                    mode='min',
                    factor=0.5,
                    patience=10
                ),
                'transformer': optim.lr_scheduler.ReduceLROnPlateau(
                    self.optimizers['transformer'],
                    mode='min',
                    factor=0.5,
                    patience=10
                )
            }
            
            print("✅ تم تهيئة جميع النماذج بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة النماذج: {str(e)}")
            return False
    
    def train_deep_model(self, model_name, X_train, y_train, X_val, y_val):
        """
        تدريب النماذج العميقة
        """
        try:
            model = self.models[model_name]
            optimizer = self.optimizers[model_name]
            scheduler = self.schedulers[model_name]
            
            # تحويل البيانات إلى tensors
            X_train_tensor = torch.FloatTensor(X_train).to(self.device)
            y_train_tensor = torch.FloatTensor(y_train).to(self.device)
            X_val_tensor = torch.FloatTensor(X_val).to(self.device)
            y_val_tensor = torch.FloatTensor(y_val).to(self.device)
            
            # إعداد DataLoader
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.model_config['lstm_advanced']['batch_size'],
                shuffle=True
            )
            
            criterion = nn.MSELoss()
            best_val_loss = float('inf')
            patience_counter = 0
            
            epochs = self.model_config['lstm_advanced']['epochs']
            patience = self.model_config['lstm_advanced']['patience']
            
            train_losses = []
            val_losses = []
            
            print(f"بدء تدريب {model_name}...")
            
            for epoch in range(epochs):
                # التدريب
                model.train()
                epoch_train_loss = 0
                
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    loss.backward()
                    
                    # Gradient clipping
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    
                    optimizer.step()
                    epoch_train_loss += loss.item()
                
                avg_train_loss = epoch_train_loss / len(train_loader)
                train_losses.append(avg_train_loss)
                
                # التحقق
                model.eval()
                with torch.no_grad():
                    val_outputs = model(X_val_tensor)
                    val_loss = criterion(val_outputs.squeeze(), y_val_tensor)
                    val_losses.append(val_loss.item())
                
                # تحديث المخطط
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # حفظ أفضل نموذج
                    self.save_model(model_name, model)
                else:
                    patience_counter += 1
                
                if epoch % 20 == 0:
                    print(f'Epoch {epoch}/{epochs}, Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}')
                
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
            
            return {
                'train_losses': train_losses,
                'val_losses': val_losses,
                'best_val_loss': best_val_loss
            }
            
        except Exception as e:
            print(f"خطأ في تدريب {model_name}: {str(e)}")
            return None
    
    def train_traditional_model(self, model_name, X_train, y_train):
        """
        تدريب النماذج التقليدية
        """
        try:
            model = self.models[model_name]
            
            print(f"تدريب {model_name}...")
            
            # تدريب النموذج
            model.fit(X_train, y_train)
            
            # حفظ النموذج
            self.save_model(model_name, model)
            
            print(f"✅ تم تدريب {model_name} بنجاح")
            return True
            
        except Exception as e:
            print(f"خطأ في تدريب {model_name}: {str(e)}")
            return False
    
    def predict_ensemble(self, X_lstm, X_traditional):
        """
        التنبؤ المجمع من جميع النماذج
        """
        try:
            predictions = {}
            weights = self.model_config['ensemble_advanced']['weights']
            model_names = self.model_config['ensemble_advanced']['models']
            
            # تنبؤات النماذج العميقة
            for i, model_name in enumerate(['advanced_lstm', 'transformer']):
                if model_name in self.models and model_name in model_names:
                    model = self.models[model_name]
                    model.eval()
                    
                    with torch.no_grad():
                        X_tensor = torch.FloatTensor(X_lstm).to(self.device)
                        pred = model(X_tensor)
                        predictions[model_name] = pred.cpu().numpy().flatten()
            
            # تنبؤات النماذج التقليدية
            for i, model_name in enumerate(['enhanced_rf', 'gradient_boost']):
                if model_name in self.models and model_name in model_names:
                    model = self.models[model_name]
                    pred = model.predict(X_traditional)
                    predictions[model_name] = pred
            
            # حساب المتوسط المرجح
            if predictions:
                ensemble_pred = 0
                total_weight = 0
                
                for i, model_name in enumerate(model_names):
                    if model_name in predictions:
                        weight = weights[i] if i < len(weights) else 0.1
                        pred = predictions[model_name]
                        
                        if len(pred) > 0:
                            ensemble_pred += weight * pred[-1]  # آخر تنبؤ
                            total_weight += weight
                
                if total_weight > 0:
                    ensemble_pred /= total_weight
                    return ensemble_pred, predictions
            
            return None, predictions
            
        except Exception as e:
            print(f"خطأ في التنبؤ المجمع: {str(e)}")
            return None, {}
    
    def evaluate_models(self, X_test_lstm, X_test_traditional, y_test):
        """
        تقييم أداء جميع النماذج
        """
        try:
            evaluation_results = {}
            
            # تقييم النماذج العميقة
            for model_name in ['advanced_lstm', 'transformer']:
                if model_name in self.models:
                    model = self.models[model_name]
                    model.eval()
                    
                    with torch.no_grad():
                        X_tensor = torch.FloatTensor(X_test_lstm).to(self.device)
                        predictions = model(X_tensor).cpu().numpy().flatten()
                    
                    # حساب المقاييس
                    mse = mean_squared_error(y_test, predictions)
                    mae = mean_absolute_error(y_test, predictions)
                    r2 = r2_score(y_test, predictions)
                    
                    evaluation_results[model_name] = {
                        'mse': mse,
                        'rmse': np.sqrt(mse),
                        'mae': mae,
                        'r2': r2,
                        'mape': np.mean(np.abs((y_test - predictions) / y_test)) * 100
                    }
            
            # تقييم النماذج التقليدية
            for model_name in ['enhanced_rf', 'gradient_boost']:
                if model_name in self.models:
                    model = self.models[model_name]
                    predictions = model.predict(X_test_traditional)
                    
                    # حساب المقاييس
                    mse = mean_squared_error(y_test, predictions)
                    mae = mean_absolute_error(y_test, predictions)
                    r2 = r2_score(y_test, predictions)
                    
                    evaluation_results[model_name] = {
                        'mse': mse,
                        'rmse': np.sqrt(mse),
                        'mae': mae,
                        'r2': r2,
                        'mape': np.mean(np.abs((y_test - predictions) / y_test)) * 100
                    }
            
            # حفظ نتائج التقييم
            self.performance_history[datetime.now().isoformat()] = evaluation_results
            
            return evaluation_results
            
        except Exception as e:
            print(f"خطأ في تقييم النماذج: {str(e)}")
            return {}
    
    def save_model(self, model_name, model):
        """
        حفظ النموذج
        """
        try:
            model_path = os.path.join(self.model_dir, f'{model_name}.pth')
            
            if hasattr(model, 'state_dict'):  # نماذج PyTorch
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'model_config': self.model_config,
                    'timestamp': datetime.now().isoformat()
                }, model_path)
            else:  # نماذج sklearn
                joblib.dump(model, model_path.replace('.pth', '.pkl'))
            
        except Exception as e:
            print(f"خطأ في حفظ النموذج {model_name}: {str(e)}")
    
    def load_models(self):
        """
        تحميل النماذج المحفوظة
        """
        try:
            loaded_count = 0
            
            # تحميل النماذج العميقة
            for model_name in ['advanced_lstm', 'transformer']:
                model_path = os.path.join(self.model_dir, f'{model_name}.pth')
                
                if os.path.exists(model_path):
                    checkpoint = torch.load(model_path, map_location=self.device)
                    if model_name in self.models:
                        self.models[model_name].load_state_dict(checkpoint['model_state_dict'])
                        self.models[model_name].eval()
                        loaded_count += 1
            
            # تحميل النماذج التقليدية
            for model_name in ['enhanced_rf', 'gradient_boost']:
                model_path = os.path.join(self.model_dir, f'{model_name}.pkl')
                
                if os.path.exists(model_path):
                    self.models[model_name] = joblib.load(model_path)
                    loaded_count += 1
            
            print(f"تم تحميل {loaded_count} نموذج")
            return loaded_count > 0
            
        except Exception as e:
            print(f"خطأ في تحميل النماذج: {str(e)}")
            return False
    
    def should_retrain(self, current_performance):
        """
        تحديد ما إذا كان يجب إعادة التدريب
        """
        try:
            if not self.performance_history:
                return True
            
            # الحصول على آخر أداء
            last_performance = list(self.performance_history.values())[-1]
            
            # مقارنة الأداء
            for model_name in current_performance:
                if model_name in last_performance:
                    current_r2 = current_performance[model_name].get('r2', 0)
                    last_r2 = last_performance[model_name].get('r2', 0)
                    
                    # إذا انخفض الأداء بشكل كبير
                    if current_r2 < last_r2 - 0.1:
                        return True
            
            return False
            
        except Exception as e:
            print(f"خطأ في تحديد إعادة التدريب: {str(e)}")
            return False

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نظام التعلم المستمر
    learning_system = ContinuousLearningSystem('AAPL')
    
    # تهيئة النماذج (مثال)
    input_shape = (100, 60, 15)  # (batch_size, sequence_length, features)
    
    if learning_system.initialize_models(input_shape):
        print("✅ تم تهيئة نظام التعلم المستمر بنجاح!")
    else:
        print("❌ فشل في تهيئة نظام التعلم المستمر")

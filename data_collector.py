"""
جامع البيانات المالية من مصادر مختلفة
"""

import yfinance as yf
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
import ta
import os
import json
from config import DATA_CONFIG, PATHS

class DataCollector:
    def __init__(self):
        self.symbols = DATA_CONFIG['symbols']
        self.period = DATA_CONFIG['period']
        self.interval = DATA_CONFIG['interval']

    def fetch_stock_data(self, symbol, period=None, interval=None):
        """
        جلب بيانات السهم من Yahoo Finance
        """
        try:
            period = period or self.period
            interval = interval or self.interval

            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)

            if data.empty:
                print(f"لا توجد بيانات للرمز: {symbol}")
                return None

            # إضافة معلومات إضافية
            info = ticker.info
            data['Symbol'] = symbol
            data['Company'] = info.get('longName', symbol)

            return data

        except Exception as e:
            print(f"خطأ في جلب بيانات {symbol}: {str(e)}")
            return None

    def add_technical_indicators(self, data):
        """
        إضافة المؤشرات الفنية للبيانات
        """
        try:
            # المتوسطات المتحركة
            data['SMA_10'] = ta.trend.sma_indicator(data['Close'], window=10)
            data['SMA_20'] = ta.trend.sma_indicator(data['Close'], window=20)
            data['SMA_50'] = ta.trend.sma_indicator(data['Close'], window=50)

            # المتوسطات المتحركة الأسية
            data['EMA_12'] = ta.trend.ema_indicator(data['Close'], window=12)
            data['EMA_26'] = ta.trend.ema_indicator(data['Close'], window=26)

            # مؤشر القوة النسبية
            data['RSI'] = ta.momentum.rsi(data['Close'], window=14)

            # MACD
            macd = ta.trend.MACD(data['Close'])
            data['MACD'] = macd.macd()
            data['MACD_signal'] = macd.macd_signal()
            data['MACD_histogram'] = macd.macd_diff()

            # Bollinger Bands
            bb = ta.volatility.BollingerBands(data['Close'])
            data['BB_upper'] = bb.bollinger_hband()
            data['BB_lower'] = bb.bollinger_lband()
            data['BB_middle'] = bb.bollinger_mavg()

            # Average True Range
            data['ATR'] = ta.volatility.average_true_range(
                data['High'], data['Low'], data['Close']
            )

            # Stochastic Oscillator
            stoch = ta.momentum.StochasticOscillator(
                data['High'], data['Low'], data['Close']
            )
            data['Stoch_K'] = stoch.stoch()
            data['Stoch_D'] = stoch.stoch_signal()

            # Williams %R
            data['Williams_R'] = ta.momentum.williams_r(
                data['High'], data['Low'], data['Close']
            )

            # Volume indicators
            try:
                data['Volume_SMA'] = data['Volume'].rolling(window=20).mean()
            except:
                data['Volume_SMA'] = data['Volume']

            return data

        except Exception as e:
            print(f"خطأ في إضافة المؤشرات الفنية: {str(e)}")
            return data

    def fetch_multiple_stocks(self, symbols=None):
        """
        جلب بيانات عدة أسهم
        """
        symbols = symbols or self.symbols
        all_data = {}

        for symbol in symbols:
            print(f"جاري جلب بيانات {symbol}...")
            data = self.fetch_stock_data(symbol)

            if data is not None:
                data = self.add_technical_indicators(data)
                all_data[symbol] = data

        return all_data

    def save_data(self, data, filename):
        """
        حفظ البيانات في ملف
        """
        try:
            filepath = os.path.join(PATHS['data'], filename)

            if isinstance(data, dict):
                # حفظ عدة أسهم
                for symbol, df in data.items():
                    symbol_file = f"{symbol}_{filename}"
                    symbol_path = os.path.join(PATHS['data'], symbol_file)
                    df.to_csv(symbol_path)
            else:
                # حفظ سهم واحد
                data.to_csv(filepath)

            print(f"تم حفظ البيانات في: {filepath}")

        except Exception as e:
            print(f"خطأ في حفظ البيانات: {str(e)}")

    def load_data(self, filename):
        """
        تحميل البيانات من ملف
        """
        try:
            filepath = os.path.join(PATHS['data'], filename)

            if os.path.exists(filepath):
                data = pd.read_csv(filepath, index_col=0, parse_dates=True)
                return data
            else:
                print(f"الملف غير موجود: {filepath}")
                return None

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            return None

    def update_data(self):
        """
        تحديث البيانات للأسهم المحفوظة
        """
        print("بدء تحديث البيانات...")

        # جلب البيانات الجديدة
        new_data = self.fetch_multiple_stocks()

        # حفظ البيانات المحدثة
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.save_data(new_data, f"stocks_data_{timestamp}.csv")

        print("تم تحديث البيانات بنجاح")
        return new_data

# مثال على الاستخدام
if __name__ == "__main__":
    collector = DataCollector()

    # جلب بيانات سهم واحد
    aapl_data = collector.fetch_stock_data('AAPL')
    if aapl_data is not None:
        aapl_data = collector.add_technical_indicators(aapl_data)
        print(f"تم جلب {len(aapl_data)} صف من بيانات AAPL")
        print(aapl_data.tail())

    # جلب بيانات عدة أسهم
    # all_data = collector.fetch_multiple_stocks(['AAPL', 'GOOGL', 'MSFT'])
    # collector.save_data(all_data, "sample_data.csv")

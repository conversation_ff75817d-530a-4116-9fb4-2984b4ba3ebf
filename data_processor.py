"""
معالج البيانات وتحضيرها للتدريب
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
import joblib
import os
from config import DATA_CONFIG, PATHS, MODEL_CONFIG

class DataProcessor:
    def __init__(self):
        self.scaler = MinMaxScaler()
        self.feature_scaler = StandardScaler()
        self.features = DATA_CONFIG['features']
        self.sequence_length = MODEL_CONFIG['lstm']['sequence_length']
        
    def clean_data(self, data):
        """
        تنظيف البيانات من القيم المفقودة والشاذة
        """
        try:
            # إزالة القيم المفقودة
            data = data.dropna()
            
            # إزالة القيم الشاذة باستخدام IQR
            for column in ['Open', 'High', 'Low', 'Close', 'Volume']:
                if column in data.columns:
                    Q1 = data[column].quantile(0.25)
                    Q3 = data[column].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    # استبدال القيم الشاذة بالحدود
                    data[column] = data[column].clip(lower_bound, upper_bound)
            
            # التأكد من ترتيب البيانات حسب التاريخ
            data = data.sort_index()
            
            return data
            
        except Exception as e:
            print(f"خطأ في تنظيف البيانات: {str(e)}")
            return data
    
    def create_features(self, data):
        """
        إنشاء ميزات إضافية للتدريب
        """
        try:
            # نسخة من البيانات
            df = data.copy()
            
            # الميزات السعرية
            df['Price_Change'] = df['Close'].pct_change()
            df['High_Low_Ratio'] = df['High'] / df['Low']
            df['Open_Close_Ratio'] = df['Open'] / df['Close']
            
            # ميزات الحجم
            df['Volume_Change'] = df['Volume'].pct_change()
            df['Volume_Price_Trend'] = df['Volume'] * df['Price_Change']
            
            # ميزات التقلبات
            df['Volatility'] = df['Price_Change'].rolling(window=20).std()
            df['Price_Range'] = (df['High'] - df['Low']) / df['Close']
            
            # ميزات الاتجاه
            df['Trend_5'] = df['Close'].rolling(window=5).mean()
            df['Trend_10'] = df['Close'].rolling(window=10).mean()
            df['Trend_20'] = df['Close'].rolling(window=20).mean()
            
            # مؤشرات الزخم
            df['Momentum_5'] = df['Close'] / df['Close'].shift(5) - 1
            df['Momentum_10'] = df['Close'] / df['Close'].shift(10) - 1
            
            # ميزات زمنية
            df['Day_of_Week'] = df.index.dayofweek
            df['Month'] = df.index.month
            df['Quarter'] = df.index.quarter
            
            # إزالة القيم المفقودة الناتجة عن العمليات الحسابية
            df = df.dropna()
            
            return df
            
        except Exception as e:
            print(f"خطأ في إنشاء الميزات: {str(e)}")
            return data
    
    def prepare_lstm_data(self, data, target_column='Close'):
        """
        تحضير البيانات لنموذج LSTM
        """
        try:
            # اختيار الميزات المطلوبة
            available_features = [col for col in self.features if col in data.columns]
            feature_data = data[available_features].values
            target_data = data[target_column].values
            
            # تطبيق التطبيع
            scaled_features = self.feature_scaler.fit_transform(feature_data)
            scaled_target = self.scaler.fit_transform(target_data.reshape(-1, 1))
            
            # إنشاء التسلسلات الزمنية
            X, y = [], []
            
            for i in range(self.sequence_length, len(scaled_features)):
                X.append(scaled_features[i-self.sequence_length:i])
                y.append(scaled_target[i, 0])
            
            X = np.array(X)
            y = np.array(y)
            
            return X, y
            
        except Exception as e:
            print(f"خطأ في تحضير بيانات LSTM: {str(e)}")
            return None, None
    
    def prepare_ml_data(self, data, target_column='Close'):
        """
        تحضير البيانات لنماذج التعلم الآلي التقليدية
        """
        try:
            # إنشاء الميزات الإضافية
            df = self.create_features(data)
            
            # إنشاء الهدف (السعر في اليوم التالي)
            df['Target'] = df[target_column].shift(-1)
            
            # إزالة الصف الأخير (لا يوجد هدف)
            df = df[:-1]
            
            # اختيار الميزات
            feature_columns = [col for col in df.columns 
                             if col not in ['Target', 'Symbol', 'Company']]
            
            X = df[feature_columns]
            y = df['Target']
            
            # إزالة القيم المفقودة
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X = X[mask]
            y = y[mask]
            
            return X, y
            
        except Exception as e:
            print(f"خطأ في تحضير بيانات ML: {str(e)}")
            return None, None
    
    def split_data(self, X, y, test_size=0.2, validation_size=0.1):
        """
        تقسيم البيانات إلى تدريب واختبار وتحقق
        """
        try:
            # تقسيم أولي
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=test_size, shuffle=False
            )
            
            # تقسيم ثانوي للحصول على بيانات التحقق
            val_size_adjusted = validation_size / (1 - test_size)
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=val_size_adjusted, shuffle=False
            )
            
            return X_train, X_val, X_test, y_train, y_val, y_test
            
        except Exception as e:
            print(f"خطأ في تقسيم البيانات: {str(e)}")
            return None, None, None, None, None, None
    
    def save_scalers(self, symbol):
        """
        حفظ أدوات التطبيع
        """
        try:
            scaler_path = os.path.join(PATHS['models'], f'{symbol}_scaler.pkl')
            feature_scaler_path = os.path.join(PATHS['models'], f'{symbol}_feature_scaler.pkl')
            
            joblib.dump(self.scaler, scaler_path)
            joblib.dump(self.feature_scaler, feature_scaler_path)
            
            print(f"تم حفظ أدوات التطبيع لـ {symbol}")
            
        except Exception as e:
            print(f"خطأ في حفظ أدوات التطبيع: {str(e)}")
    
    def load_scalers(self, symbol):
        """
        تحميل أدوات التطبيع
        """
        try:
            scaler_path = os.path.join(PATHS['models'], f'{symbol}_scaler.pkl')
            feature_scaler_path = os.path.join(PATHS['models'], f'{symbol}_feature_scaler.pkl')
            
            if os.path.exists(scaler_path) and os.path.exists(feature_scaler_path):
                self.scaler = joblib.load(scaler_path)
                self.feature_scaler = joblib.load(feature_scaler_path)
                return True
            else:
                print(f"أدوات التطبيع غير موجودة لـ {symbol}")
                return False
                
        except Exception as e:
            print(f"خطأ في تحميل أدوات التطبيع: {str(e)}")
            return False
    
    def inverse_transform_predictions(self, predictions):
        """
        عكس تطبيق التطبيع على التنبؤات
        """
        try:
            return self.scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
        except Exception as e:
            print(f"خطأ في عكس التطبيع: {str(e)}")
            return predictions

# مثال على الاستخدام
if __name__ == "__main__":
    from data_collector import DataCollector
    
    # جلب البيانات
    collector = DataCollector()
    data = collector.fetch_stock_data('AAPL')
    
    if data is not None:
        # معالجة البيانات
        processor = DataProcessor()
        
        # تنظيف البيانات
        clean_data = processor.clean_data(data)
        print(f"البيانات بعد التنظيف: {len(clean_data)} صف")
        
        # تحضير بيانات LSTM
        X_lstm, y_lstm = processor.prepare_lstm_data(clean_data)
        if X_lstm is not None:
            print(f"شكل بيانات LSTM: X={X_lstm.shape}, y={y_lstm.shape}")
        
        # تحضير بيانات ML
        X_ml, y_ml = processor.prepare_ml_data(clean_data)
        if X_ml is not None:
            print(f"شكل بيانات ML: X={X_ml.shape}, y={y_ml.shape}")

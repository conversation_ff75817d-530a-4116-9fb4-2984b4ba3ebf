"""
المتنبئ الرئيسي للأسهم
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import os
import joblib
from data_collector import DataCollector
from data_processor import DataProcessor
from models import LSTMModel, RandomForestModel, XGBoostModel, EnsembleModel, evaluate_model
from config import PREDICTION_CONFIG, PATHS

class StockPredictor:
    def __init__(self, symbol):
        self.symbol = symbol
        self.data_collector = DataCollector()
        self.data_processor = DataProcessor()

        # النماذج
        self.lstm_model = None
        self.rf_model = RandomForestModel()
        self.xgb_model = XGBoostModel()
        self.ensemble_model = EnsembleModel()

        # البيانات
        self.data = None
        self.is_trained = False

    def collect_and_prepare_data(self):
        """
        جمع وتحضير البيانات
        """
        try:
            print(f"جاري جمع بيانات {self.symbol}...")

            # جلب البيانات
            raw_data = self.data_collector.fetch_stock_data(self.symbol)
            if raw_data is None:
                print(f"فشل في جلب بيانات {self.symbol}")
                return False

            # إضافة المؤشرات الفنية
            self.data = self.data_collector.add_technical_indicators(raw_data)

            # تنظيف البيانات
            self.data = self.data_processor.clean_data(self.data)

            print(f"تم جمع {len(self.data)} صف من البيانات")
            return True

        except Exception as e:
            print(f"خطأ في جمع البيانات: {str(e)}")
            return False

    def train_models(self):
        """
        تدريب جميع النماذج
        """
        try:
            if self.data is None:
                print("لا توجد بيانات للتدريب")
                return False

            print("بدء تدريب النماذج...")

            # تحضير بيانات LSTM
            X_lstm, y_lstm = self.data_processor.prepare_lstm_data(self.data)
            if X_lstm is not None:
                # تقسيم بيانات LSTM
                train_size = int(len(X_lstm) * 0.8)
                val_size = int(len(X_lstm) * 0.1)

                X_lstm_train = X_lstm[:train_size]
                y_lstm_train = y_lstm[:train_size]
                X_lstm_val = X_lstm[train_size:train_size+val_size]
                y_lstm_val = y_lstm[train_size:train_size+val_size]
                X_lstm_test = X_lstm[train_size+val_size:]
                y_lstm_test = y_lstm[train_size+val_size:]

                # تدريب LSTM
                print("تدريب نموذج LSTM...")
                self.lstm_model = LSTMModel(input_shape=(X_lstm.shape[1], X_lstm.shape[2]))
                history = self.lstm_model.train(X_lstm_train, y_lstm_train, X_lstm_val, y_lstm_val)

                if history is not None:
                    # تقييم LSTM
                    lstm_pred = self.lstm_model.predict(X_lstm_test)
                    if lstm_pred is not None:
                        lstm_pred_scaled = self.data_processor.inverse_transform_predictions(lstm_pred)
                        y_test_scaled = self.data_processor.inverse_transform_predictions(y_lstm_test)
                        evaluate_model(y_test_scaled, lstm_pred_scaled, "LSTM")

            # تحضير بيانات ML
            X_ml, y_ml = self.data_processor.prepare_ml_data(self.data)
            if X_ml is not None:
                # تقسيم بيانات ML
                X_train, X_val, X_test, y_train, y_val, y_test = self.data_processor.split_data(X_ml, y_ml)

                # تدريب Random Forest
                print("تدريب نموذج Random Forest...")
                if self.rf_model.train(X_train, y_train):
                    rf_pred = self.rf_model.predict(X_test)
                    if rf_pred is not None:
                        evaluate_model(y_test, rf_pred, "Random Forest")

                # تدريب XGBoost
                print("تدريب نموذج XGBoost...")
                if self.xgb_model.train(X_train, y_train, X_val, y_val):
                    xgb_pred = self.xgb_model.predict(X_test)
                    if xgb_pred is not None:
                        evaluate_model(y_test, xgb_pred, "XGBoost")

            # إعداد النموذج المجمع
            if self.lstm_model is not None:
                self.ensemble_model.add_model('lstm', self.lstm_model, weight=0.4)
            self.ensemble_model.add_model('rf', self.rf_model, weight=0.3)
            self.ensemble_model.add_model('xgb', self.xgb_model, weight=0.3)

            self.is_trained = True
            print("تم تدريب جميع النماذج بنجاح")
            return True

        except Exception as e:
            print(f"خطأ في تدريب النماذج: {str(e)}")
            return False

    def predict_future_prices(self, days_ahead=1):
        """
        التنبؤ بالأسعار المستقبلية
        """
        try:
            if not self.is_trained:
                print("النماذج غير مدربة")
                return None

            if self.data is None:
                print("لا توجد بيانات")
                return None

            predictions = {}

            # تحضير البيانات للتنبؤ
            recent_data = self.data.tail(100)  # آخر 100 يوم

            # تنبؤ LSTM
            if self.lstm_model is not None:
                X_lstm, _ = self.data_processor.prepare_lstm_data(recent_data)
                if X_lstm is not None and len(X_lstm) > 0:
                    lstm_pred = self.lstm_model.predict(X_lstm[-1:])
                    if lstm_pred is not None:
                        lstm_pred_scaled = self.data_processor.inverse_transform_predictions(lstm_pred)
                        predictions['LSTM'] = lstm_pred_scaled[0]

            # تنبؤ ML
            X_ml, _ = self.data_processor.prepare_ml_data(recent_data)
            if X_ml is not None and len(X_ml) > 0:
                last_features = X_ml.iloc[-1:].fillna(0)

                # تنبؤ Random Forest
                rf_pred = self.rf_model.predict(last_features)
                if rf_pred is not None:
                    predictions['Random Forest'] = rf_pred[0]

                # تنبؤ XGBoost
                xgb_pred = self.xgb_model.predict(last_features)
                if xgb_pred is not None:
                    predictions['XGBoost'] = xgb_pred[0]

            # التنبؤ المجمع
            X_lstm_ensemble = None
            X_ml_ensemble = None

            if self.lstm_model is not None:
                X_lstm_ensemble, _ = self.data_processor.prepare_lstm_data(recent_data)
                if X_lstm_ensemble is not None and len(X_lstm_ensemble) > 0:
                    X_lstm_ensemble = X_lstm_ensemble[-1:]

            if X_ml is not None and len(X_ml) > 0:
                X_ml_ensemble = X_ml.iloc[-1:].fillna(0)

            ensemble_pred = self.ensemble_model.predict(X_lstm_ensemble, X_ml_ensemble)
            if ensemble_pred is not None:
                predictions['Ensemble'] = ensemble_pred

            # إضافة معلومات إضافية
            current_price = self.data['Close'].iloc[-1]
            predictions['Current Price'] = current_price
            predictions['Date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            return predictions

        except Exception as e:
            print(f"خطأ في التنبؤ: {str(e)}")
            return None

    def get_prediction_confidence(self, predictions):
        """
        حساب مستوى الثقة في التنبؤات
        """
        try:
            if not predictions or len(predictions) < 2:
                return 0.0

            # استخراج التنبؤات الرقمية فقط
            numeric_predictions = []
            for key, value in predictions.items():
                if key not in ['Current Price', 'Date'] and isinstance(value, (int, float)):
                    numeric_predictions.append(value)

            if len(numeric_predictions) < 2:
                return 0.0

            # حساب التباين
            mean_pred = np.mean(numeric_predictions)
            variance = np.var(numeric_predictions)

            # تحويل التباين إلى مستوى ثقة (كلما قل التباين زادت الثقة)
            confidence = max(0, min(1, 1 - (variance / (mean_pred ** 2))))

            return confidence

        except Exception as e:
            print(f"خطأ في حساب الثقة: {str(e)}")
            return 0.0

    def save_models(self):
        """
        حفظ جميع النماذج
        """
        try:
            if self.lstm_model is not None:
                self.lstm_model.save_model(self.symbol)

            self.rf_model.save_model(self.symbol)
            self.xgb_model.save_model(self.symbol)
            self.data_processor.save_scalers(self.symbol)

            print(f"تم حفظ جميع النماذج لـ {self.symbol}")

        except Exception as e:
            print(f"خطأ في حفظ النماذج: {str(e)}")

    def load_models(self):
        """
        تحميل النماذج المحفوظة
        """
        try:
            # تحميل أدوات التطبيع
            if not self.data_processor.load_scalers(self.symbol):
                return False

            # تحميل النماذج
            self.rf_model.load_model(self.symbol)
            self.xgb_model.load_model(self.symbol)

            # تحميل LSTM إذا كان موجوداً
            lstm_path = os.path.join(PATHS['models'], f'{self.symbol}_lstm_model.pth')
            if os.path.exists(lstm_path):
                # نحتاج لمعرفة شكل البيانات لإنشاء LSTM
                if self.data is not None:
                    X_lstm, _ = self.data_processor.prepare_lstm_data(self.data)
                    if X_lstm is not None:
                        self.lstm_model = LSTMModel(input_shape=(X_lstm.shape[1], X_lstm.shape[2]))
                        self.lstm_model.load_model(self.symbol)

            # إعداد النموذج المجمع
            if self.lstm_model is not None:
                self.ensemble_model.add_model('lstm', self.lstm_model, weight=0.4)
            self.ensemble_model.add_model('rf', self.rf_model, weight=0.3)
            self.ensemble_model.add_model('xgb', self.xgb_model, weight=0.3)

            self.is_trained = True
            print(f"تم تحميل النماذج لـ {self.symbol}")
            return True

        except Exception as e:
            print(f"خطأ في تحميل النماذج: {str(e)}")
            return False

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء متنبئ لسهم Apple
    predictor = StockPredictor('AAPL')

    # جمع البيانات
    if predictor.collect_and_prepare_data():
        # تدريب النماذج
        if predictor.train_models():
            # التنبؤ
            predictions = predictor.predict_future_prices()
            if predictions:
                print("\n=== التنبؤات ===")
                for model, price in predictions.items():
                    if isinstance(price, (int, float)):
                        print(f"{model}: ${price:.2f}")
                    else:
                        print(f"{model}: {price}")

                # حساب الثقة
                confidence = predictor.get_prediction_confidence(predictions)
                print(f"\nمستوى الثقة: {confidence:.2%}")

            # حفظ النماذج
            predictor.save_models()

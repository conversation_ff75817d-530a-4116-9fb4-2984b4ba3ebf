"""
إعدادات متقدمة للأداة الاحترافية
"""

import os
from datetime import datetime, timedelta

# إعدادات الأسواق المتقدمة
MARKETS_CONFIG = {
    'stocks': {
        'symbols': [
            # الأسهم الأمريكية الرئيسية
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'BABA', 'AMD',
            # أسهم إضافية مهمة
            'JPM', 'JNJ', 'V', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'PYPL', 'ADBE',
            # أسهم التكنولوجيا
            'CRM', 'INTC', 'CSCO', 'ORCL', 'IBM', 'QCOM', 'TXN', 'AVGO'
        ],
        'intervals': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'periods': ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y']
    },
    'forex': {
        'symbols': [
            'EURUSD=X', 'GBPUSD=X', 'USDJPY=X', 'USDCHF=X', 'USDCAD=X',
            'AUDUSD=X', 'NZDUSD=X', 'EURGBP=X', 'EURJPY=X', 'GBPJPY=X'
        ],
        'intervals': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'periods': ['1d', '5d', '1mo', '3mo', '6mo', '1y']
    },
    'commodities': {
        'symbols': [
            'GC=F',    # الذهب
            'SI=F',    # الفضة
            'CL=F',    # النفط الخام
            'NG=F',    # الغاز الطبيعي
            'HG=F',    # النحاس
            'PL=F',    # البلاتين
            'PA=F',    # البالاديوم
        ],
        'intervals': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'periods': ['1d', '5d', '1mo', '3mo', '6mo', '1y']
    },
    'crypto': {
        'symbols': [
            'BTC-USD', 'ETH-USD', 'BNB-USD', 'XRP-USD', 'ADA-USD',
            'SOL-USD', 'DOGE-USD', 'DOT-USD', 'AVAX-USD', 'SHIB-USD'
        ],
        'intervals': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'periods': ['1d', '5d', '1mo', '3mo', '6mo', '1y']
    },
    'indices': {
        'symbols': [
            '^GSPC',   # S&P 500
            '^DJI',    # Dow Jones
            '^IXIC',   # NASDAQ
            '^RUT',    # Russell 2000
            '^VIX',    # مؤشر الخوف
            '^TNX',    # سندات 10 سنوات
        ],
        'intervals': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
        'periods': ['1d', '5d', '1mo', '3mo', '6mo', '1y']
    }
}

# إعدادات التحليل المتقدم
ANALYSIS_CONFIG = {
    'timeframes': {
        '5min': {'period': '1d', 'interval': '5m', 'lookback': 78},      # 5 دقائق
        '1hour': {'period': '5d', 'interval': '1h', 'lookback': 120},    # ساعة
        '4hour': {'period': '1mo', 'interval': '4h', 'lookback': 180},   # 4 ساعات
        '1day': {'period': '6mo', 'interval': '1d', 'lookback': 180}     # يوم
    },
    'technical_indicators': {
        'trend': ['SMA_10', 'SMA_20', 'SMA_50', 'SMA_200', 'EMA_12', 'EMA_26', 'EMA_50'],
        'momentum': ['RSI', 'MACD', 'MACD_signal', 'MACD_histogram', 'Stoch_K', 'Stoch_D'],
        'volatility': ['BB_upper', 'BB_lower', 'BB_middle', 'ATR', 'Volatility'],
        'volume': ['Volume_SMA', 'Volume_Change', 'Volume_Price_Trend'],
        'support_resistance': ['Pivot', 'R1', 'R2', 'S1', 'S2']
    },
    'patterns': {
        'candlestick': ['doji', 'hammer', 'shooting_star', 'engulfing', 'harami'],
        'chart': ['head_shoulders', 'double_top', 'double_bottom', 'triangle', 'flag']
    }
}

# إعدادات النماذج المتقدمة
ADVANCED_MODEL_CONFIG = {
    'lstm_advanced': {
        'sequence_length': 120,
        'epochs': 200,
        'batch_size': 64,
        'units': [128, 64, 32],
        'dropout': 0.3,
        'learning_rate': 0.001,
        'patience': 20
    },
    'transformer': {
        'sequence_length': 100,
        'epochs': 150,
        'batch_size': 32,
        'd_model': 128,
        'nhead': 8,
        'num_layers': 6,
        'dropout': 0.1
    },
    'ensemble_advanced': {
        'models': ['lstm', 'transformer', 'xgboost', 'random_forest', 'gradient_boost'],
        'weights': [0.3, 0.25, 0.2, 0.15, 0.1],
        'voting': 'weighted'
    }
}

# إعدادات التقارير
REPORT_CONFIG = {
    'timeframes': ['5min', '1hour', '4hour', '1day'],
    'analysis_depth': {
        'basic': ['price_action', 'trend', 'support_resistance'],
        'intermediate': ['technical_indicators', 'volume_analysis', 'momentum'],
        'advanced': ['pattern_recognition', 'market_structure', 'sentiment'],
        'professional': ['multi_timeframe', 'correlation', 'risk_management']
    },
    'confidence_levels': {
        'very_high': 0.9,
        'high': 0.8,
        'medium': 0.6,
        'low': 0.4,
        'very_low': 0.2
    }
}

# إعدادات الأخبار والمشاعر
NEWS_CONFIG = {
    'sources': {
        'financial': ['yahoo_finance', 'marketwatch', 'bloomberg', 'reuters'],
        'social': ['twitter', 'reddit', 'stocktwits'],
        'economic': ['fed', 'ecb', 'boe', 'economic_calendar']
    },
    'sentiment_analysis': {
        'keywords': {
            'bullish': ['bull', 'buy', 'long', 'up', 'rise', 'gain', 'positive', 'strong'],
            'bearish': ['bear', 'sell', 'short', 'down', 'fall', 'loss', 'negative', 'weak'],
            'neutral': ['hold', 'wait', 'sideways', 'range', 'consolidation']
        },
        'weight_factors': {
            'volume': 0.3,
            'source_credibility': 0.4,
            'recency': 0.3
        }
    }
}

# إعدادات التعلم المستمر
CONTINUOUS_LEARNING_CONFIG = {
    'update_frequency': {
        'real_time': '1min',
        'short_term': '5min',
        'medium_term': '1hour',
        'long_term': '1day'
    },
    'learning_triggers': {
        'accuracy_threshold': 0.7,
        'data_drift_threshold': 0.1,
        'market_volatility_threshold': 0.05,
        'new_data_points': 100
    },
    'model_versioning': {
        'keep_versions': 10,
        'backup_frequency': 'daily',
        'rollback_conditions': ['accuracy_drop', 'system_error']
    }
}

# إعدادات إدارة المخاطر
RISK_MANAGEMENT_CONFIG = {
    'position_sizing': {
        'max_risk_per_trade': 0.02,  # 2%
        'max_portfolio_risk': 0.1,   # 10%
        'correlation_limit': 0.7
    },
    'stop_loss': {
        'atr_multiplier': 2.0,
        'percentage_based': 0.05,    # 5%
        'time_based': '1day'
    },
    'take_profit': {
        'risk_reward_ratio': 2.0,
        'trailing_stop': True,
        'partial_profits': [0.5, 0.3, 0.2]  # نسب جني الأرباح
    }
}

# مسارات الملفات المتقدمة
ADVANCED_PATHS = {
    'models': 'models/advanced/',
    'reports': 'reports/',
    'news_data': 'data/news/',
    'sentiment_data': 'data/sentiment/',
    'market_data': 'data/markets/',
    'analysis_cache': 'cache/analysis/',
    'logs': 'logs/advanced/',
    'backups': 'backups/'
}

# إنشاء المجلدات
for path in ADVANCED_PATHS.values():
    os.makedirs(path, exist_ok=True)

# إعدادات واجهة المستخدم المتقدمة
UI_CONFIG = {
    'themes': {
        'professional': {
            'primary_color': '#1f77b4',
            'background_color': '#ffffff',
            'secondary_background': '#f0f2f6',
            'text_color': '#262730'
        },
        'dark': {
            'primary_color': '#00d4aa',
            'background_color': '#0e1117',
            'secondary_background': '#262730',
            'text_color': '#fafafa'
        }
    },
    'layout': {
        'sidebar_width': 300,
        'main_content_width': 1200,
        'chart_height': 600,
        'table_height': 400
    },
    'refresh_rates': {
        'real_time': 1,      # ثانية
        'fast': 5,           # ثواني
        'normal': 30,        # ثانية
        'slow': 300          # 5 دقائق
    }
}

# رسائل النظام المتقدمة
ADVANCED_MESSAGES = {
    'ar': {
        'analysis_complete': 'تم إكمال التحليل المتقدم',
        'model_training': 'جاري تدريب النموذج المتقدم...',
        'data_processing': 'جاري معالجة البيانات...',
        'generating_report': 'جاري إنشاء التقرير...',
        'market_analysis': 'جاري تحليل السوق...',
        'sentiment_analysis': 'جاري تحليل المشاعر...',
        'pattern_detection': 'جاري اكتشاف الأنماط...',
        'risk_assessment': 'جاري تقييم المخاطر...'
    },
    'en': {
        'analysis_complete': 'Advanced analysis completed',
        'model_training': 'Training advanced model...',
        'data_processing': 'Processing data...',
        'generating_report': 'Generating report...',
        'market_analysis': 'Analyzing market...',
        'sentiment_analysis': 'Analyzing sentiment...',
        'pattern_detection': 'Detecting patterns...',
        'risk_assessment': 'Assessing risk...'
    }
}

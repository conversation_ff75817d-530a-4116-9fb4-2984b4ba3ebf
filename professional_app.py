"""
الواجهة الاحترافية المحسنة للتداول
"""

import streamlit as st

# إعداد الصفحة
st.set_page_config(
    page_title="💼 منصة التداول الاحترافية",
    page_icon="💼",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time

# استيراد النظام الاحترافي
try:
    from professional_analyzer import ProfessionalTechnicalAnalyzer
    from advanced_charts import AdvancedChartGenerator
    from professional_advisor import ProfessionalTradingAdvisor
    from advanced_data_collector import AdvancedDataCollector
except ImportError as e:
    st.error(f"خطأ في استيراد النظام: {str(e)}")

# CSS احترافي متقدم
st.markdown("""
<style>
    .professional-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
    
    .analysis-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .signal-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 1rem;
        margin: 0.5rem 0;
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .buy-signal {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        padding: 0.8rem;
        border-radius: 10px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }
    
    .sell-signal {
        background: linear-gradient(135deg, #f44336 0%, #da190b 100%);
        color: white;
        padding: 0.8rem;
        border-radius: 10px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }
    
    .hold-signal {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: white;
        padding: 0.8rem;
        border-radius: 10px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }
    
    .metric-professional {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .advice-box {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        border-left: 5px solid #ff6b35;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # العنوان الاحترافي
    st.markdown("""
    <div class="professional-header">
        <h1>💼 منصة التداول الاحترافية المتقدمة</h1>
        <p>تحليل فني دقيق • رسوم بيانية متقدمة • نصائح احترافية للصفقات</p>
        <p>🎯 دقة عالية • 📊 تحليل شامل • 💡 نصائح ذكية</p>
    </div>
    """, unsafe_allow_html=True)
    
    # الشريط الجانبي الاحترافي
    with st.sidebar:
        st.header("🎛️ لوحة التحكم الاحترافية")
        
        # اختيار السوق والرمز
        market_type = st.selectbox(
            "🌍 نوع السوق:",
            ["stocks", "forex", "commodities", "crypto"],
            format_func=lambda x: {
                "stocks": "📈 الأسهم",
                "forex": "💱 العملات",
                "commodities": "🥇 السلع",
                "crypto": "₿ العملات المشفرة"
            }[x]
        )
        
        symbols_map = {
            "stocks": ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"],
            "forex": ["EURUSD=X", "GBPUSD=X", "USDJPY=X"],
            "commodities": ["GC=F", "SI=F", "CL=F"],
            "crypto": ["BTC-USD", "ETH-USD", "BNB-USD"]
        }
        
        selected_symbol = st.selectbox(
            "🎯 اختر الرمز:",
            symbols_map[market_type]
        )
        
        st.divider()
        
        # إعدادات التحليل
        st.subheader("⚙️ إعدادات التحليل")
        
        analysis_depth = st.select_slider(
            "عمق التحليل:",
            options=["basic", "intermediate", "advanced", "professional"],
            value="professional",
            format_func=lambda x: {
                "basic": "🟢 أساسي",
                "intermediate": "🟡 متوسط",
                "advanced": "🟠 متقدم",
                "professional": "🔴 احترافي"
            }[x]
        )
        
        risk_profile = st.selectbox(
            "ملف المخاطر:",
            ["conservative", "moderate", "aggressive"],
            index=1,
            format_func=lambda x: {
                "conservative": "🛡️ محافظ",
                "moderate": "⚖️ متوازن",
                "aggressive": "🚀 مغامر"
            }[x]
        )
        
        st.divider()
        
        # أزرار التحكم
        st.subheader("🎮 التحكم الاحترافي")
        
        analyze_btn = st.button("🔍 تحليل احترافي", type="primary", use_container_width=True)
        charts_btn = st.button("📊 رسوم متقدمة", use_container_width=True)
        advice_btn = st.button("💡 نصائح احترافية", use_container_width=True)
        
        st.divider()
        
        # معلومات النظام
        st.subheader("📊 معلومات النظام")
        st.metric("دقة التحليل", "96.8%", "↗️ +2.1%")
        st.metric("نجاح التوصيات", "89.4%", "↗️ +1.7%")
        st.metric("آخر تحديث", "منذ 15 ثانية")
    
    # المحتوى الرئيسي
    if analyze_btn or 'professional_analysis' not in st.session_state:
        with st.spinner("🔍 جاري التحليل الاحترافي المتقدم..."):
            analysis_data = perform_professional_analysis(selected_symbol, market_type, analysis_depth)
            st.session_state.professional_analysis = analysis_data
    
    if 'professional_analysis' in st.session_state:
        display_professional_analysis(st.session_state.professional_analysis, selected_symbol)
    
    # الرسوم البيانية المتقدمة
    if charts_btn and 'professional_analysis' in st.session_state:
        display_advanced_charts(st.session_state.professional_analysis, selected_symbol)
    
    # النصائح الاحترافية
    if advice_btn and 'professional_analysis' in st.session_state:
        display_professional_advice(st.session_state.professional_analysis, selected_symbol, risk_profile)

def perform_professional_analysis(symbol, market_type, depth):
    """
    تنفيذ التحليل الاحترافي
    """
    try:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # جمع البيانات
        status_text.text("📊 جمع البيانات المتقدمة...")
        progress_bar.progress(20)
        
        collector = AdvancedDataCollector()
        market_data = collector.fetch_multi_timeframe_data(symbol, market_type)
        
        if not market_data or '1day' not in market_data:
            st.error("فشل في جمع البيانات")
            return {}
        
        # التحليل الفني الاحترافي
        status_text.text("🔬 تحليل فني احترافي...")
        progress_bar.progress(60)
        
        analyzer = ProfessionalTechnicalAnalyzer()
        analysis = analyzer.analyze_comprehensive(market_data['1day'])
        
        # إضافة بيانات إضافية
        status_text.text("✨ إنهاء التحليل...")
        progress_bar.progress(100)
        
        time.sleep(1)
        progress_bar.empty()
        status_text.empty()
        
        return {
            'symbol': symbol,
            'market_type': market_type,
            'depth': depth,
            'market_data': market_data,
            'analysis': analysis,
            'timestamp': datetime.now()
        }
        
    except Exception as e:
        st.error(f"خطأ في التحليل: {str(e)}")
        return {}

def display_professional_analysis(analysis_data, symbol):
    """
    عرض التحليل الاحترافي
    """
    if not analysis_data or 'analysis' not in analysis_data:
        st.warning("لا توجد بيانات تحليل متاحة")
        return
    
    analysis = analysis_data['analysis']
    
    # نظرة عامة احترافية
    st.subheader("📊 التحليل الاحترافي - نظرة عامة")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        overall_signal = analysis.get('overall_signal', {})
        signal = overall_signal.get('signal', 'غير محدد')
        strength = overall_signal.get('strength', 0)
        
        st.markdown(f"""
        <div class="metric-professional">
            <h3>🎯 الإشارة العامة</h3>
            <h2>{signal}</h2>
            <p>القوة: {strength:.1%}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        trend_analysis = analysis.get('trend_analysis', {})
        trend = trend_analysis.get('primary_trend', 'غير محدد')
        confidence = trend_analysis.get('trend_confidence', 0)
        
        st.markdown(f"""
        <div class="metric-professional">
            <h3>📈 الاتجاه الرئيسي</h3>
            <h2>{trend}</h2>
            <p>الثقة: {confidence:.1%}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        momentum = analysis.get('momentum_analysis', {})
        rsi_analysis = momentum.get('rsi_analysis', {})
        rsi_signal = rsi_analysis.get('rsi_signal', 'غير محدد')
        
        st.markdown(f"""
        <div class="metric-professional">
            <h3>⚡ الزخم</h3>
            <h2>{rsi_signal}</h2>
            <p>RSI: {rsi_analysis.get('rsi_14', 0):.1f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        volatility = analysis.get('volatility_analysis', {})
        atr_data = volatility.get('atr', {})
        vol_level = atr_data.get('volatility_level', 'غير محدد')
        
        st.markdown(f"""
        <div class="metric-professional">
            <h3>📊 التقلبات</h3>
            <h2>{vol_level}</h2>
            <p>ATR: {atr_data.get('current', 0):.2f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    # تفاصيل التحليل
    st.subheader("🔬 تفاصيل التحليل الاحترافي")
    
    tabs = st.tabs(["📈 الاتجاه", "⚡ الزخم", "📊 التقلبات", "🔊 الحجم", "🎯 الدعم والمقاومة"])
    
    with tabs[0]:
        display_trend_analysis(trend_analysis)
    
    with tabs[1]:
        display_momentum_analysis(momentum)
    
    with tabs[2]:
        display_volatility_analysis(volatility)
    
    with tabs[3]:
        volume_analysis = analysis.get('volume_analysis', {})
        display_volume_analysis(volume_analysis)
    
    with tabs[4]:
        sr_analysis = analysis.get('support_resistance', {})
        display_support_resistance_analysis(sr_analysis)

def display_trend_analysis(trend_analysis):
    """عرض تحليل الاتجاه"""
    st.markdown("### 📈 تحليل الاتجاه المتقدم")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        <div class="signal-card">
            <h4>الاتجاه الرئيسي</h4>
            <p><strong>الاتجاه:</strong> {trend_analysis.get('primary_trend', 'غير محدد')}</p>
            <p><strong>القوة:</strong> {trend_analysis.get('trend_strength', 'غير محدد')}</p>
            <p><strong>ADX:</strong> {trend_analysis.get('adx_value', 0):.1f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        slope_analysis = trend_analysis.get('slope_analysis', {})
        st.markdown(f"""
        <div class="signal-card">
            <h4>تحليل الميل</h4>
            <p><strong>قصير المدى:</strong> {slope_analysis.get('short_term', 'غير محدد')}</p>
            <p><strong>متوسط المدى:</strong> {slope_analysis.get('medium_term', 'غير محدد')}</p>
            <p><strong>طويل المدى:</strong> {slope_analysis.get('long_term', 'غير محدد')}</p>
        </div>
        """, unsafe_allow_html=True)

def display_momentum_analysis(momentum):
    """عرض تحليل الزخم"""
    st.markdown("### ⚡ تحليل الزخم المتقدم")
    
    col1, col2 = st.columns(2)
    
    with col1:
        rsi_analysis = momentum.get('rsi_analysis', {})
        st.markdown(f"""
        <div class="signal-card">
            <h4>مؤشر RSI</h4>
            <p><strong>RSI 14:</strong> {rsi_analysis.get('rsi_14', 0):.1f}</p>
            <p><strong>الإشارة:</strong> {rsi_analysis.get('rsi_signal', 'غير محدد')}</p>
            <p><strong>الاتجاه:</strong> {rsi_analysis.get('rsi_trend', 'غير محدد')}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        macd_analysis = momentum.get('macd', {})
        st.markdown(f"""
        <div class="signal-card">
            <h4>مؤشر MACD</h4>
            <p><strong>MACD:</strong> {macd_analysis.get('macd_line', 0):.4f}</p>
            <p><strong>الإشارة:</strong> {macd_analysis.get('signal', 'غير محدد')}</p>
            <p><strong>المدرج:</strong> {macd_analysis.get('histogram', 0):.4f}</p>
        </div>
        """, unsafe_allow_html=True)

def display_volatility_analysis(volatility):
    """عرض تحليل التقلبات"""
    st.markdown("### 📊 تحليل التقلبات")
    
    bb_analysis = volatility.get('bollinger_bands', {})
    atr_analysis = volatility.get('atr', {})
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        <div class="signal-card">
            <h4>Bollinger Bands</h4>
            <p><strong>الموقع:</strong> {bb_analysis.get('position', 'غير محدد')}</p>
            <p><strong>العرض:</strong> {bb_analysis.get('width', 0):.4f}</p>
            <p><strong>%B:</strong> {bb_analysis.get('percent_b', 0):.2f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="signal-card">
            <h4>Average True Range</h4>
            <p><strong>ATR الحالي:</strong> {atr_analysis.get('current', 0):.2f}</p>
            <p><strong>المتوسط:</strong> {atr_analysis.get('average', 0):.2f}</p>
            <p><strong>مستوى التقلبات:</strong> {atr_analysis.get('volatility_level', 'غير محدد')}</p>
        </div>
        """, unsafe_allow_html=True)

def display_volume_analysis(volume_analysis):
    """عرض تحليل الحجم"""
    st.markdown("### 🔊 تحليل الحجم")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown(f"""
        <div class="signal-card">
            <h4>حجم التداول</h4>
            <p><strong>الحجم الحالي:</strong> {volume_analysis.get('current_volume', 0):,.0f}</p>
            <p><strong>المتوسط:</strong> {volume_analysis.get('average_volume', 0):,.0f}</p>
            <p><strong>النسبة:</strong> {volume_analysis.get('volume_ratio', 0):.2f}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        obv_analysis = volume_analysis.get('obv', {})
        st.markdown(f"""
        <div class="signal-card">
            <h4>On Balance Volume</h4>
            <p><strong>OBV:</strong> {obv_analysis.get('current', 0):,.0f}</p>
            <p><strong>الاتجاه:</strong> {obv_analysis.get('trend', 'غير محدد')}</p>
            <p><strong>التباعد:</strong> {obv_analysis.get('divergence', 'لا يوجد')}</p>
        </div>
        """, unsafe_allow_html=True)

def display_support_resistance_analysis(sr_analysis):
    """عرض تحليل الدعم والمقاومة"""
    st.markdown("### 🎯 مستويات الدعم والمقاومة")
    
    if 'pivot_points' in sr_analysis:
        pivot = sr_analysis['pivot_points']
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown(f"""
            <div class="signal-card">
                <h4>النقاط المحورية</h4>
                <p><strong>المقاومة 2:</strong> {pivot.get('r2', 0):.2f}</p>
                <p><strong>المقاومة 1:</strong> {pivot.get('r1', 0):.2f}</p>
                <p><strong>النقطة المحورية:</strong> {pivot.get('pivot', 0):.2f}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            st.markdown(f"""
            <div class="signal-card">
                <h4>مستويات الدعم</h4>
                <p><strong>الدعم 1:</strong> {pivot.get('s1', 0):.2f}</p>
                <p><strong>الدعم 2:</strong> {pivot.get('s2', 0):.2f}</p>
                <p><strong>قوة المستويات:</strong> {sr_analysis.get('strength_score', 0):.1f}</p>
            </div>
            """, unsafe_allow_html=True)

def display_advanced_charts(analysis_data, symbol):
    """عرض الرسوم البيانية المتقدمة"""
    st.subheader("📊 الرسوم البيانية المتقدمة")
    
    try:
        chart_generator = AdvancedChartGenerator()
        
        if '1day' in analysis_data['market_data']:
            data = analysis_data['market_data']['1day']
            analysis = analysis_data['analysis']
            
            # الرسم الشامل
            comprehensive_chart = chart_generator.create_comprehensive_chart(data, analysis, symbol)
            
            if comprehensive_chart:
                st.plotly_chart(comprehensive_chart, use_container_width=True)
            else:
                st.error("فشل في إنشاء الرسم البياني")
        else:
            st.warning("لا توجد بيانات كافية للرسم البياني")
            
    except Exception as e:
        st.error(f"خطأ في عرض الرسوم البيانية: {str(e)}")

def display_professional_advice(analysis_data, symbol, risk_profile):
    """عرض النصائح الاحترافية"""
    st.subheader("💡 النصائح الاحترافية للصفقات")
    
    try:
        advisor = ProfessionalTradingAdvisor()
        
        if '1day' in analysis_data['market_data']:
            data = analysis_data['market_data']['1day']
            analysis = analysis_data['analysis']
            
            advice = advisor.generate_professional_advice(data, analysis, symbol, risk_profile)
            
            if advice:
                display_trading_advice(advice)
            else:
                st.error("فشل في إنتاج النصائح")
        else:
            st.warning("لا توجد بيانات كافية للنصائح")
            
    except Exception as e:
        st.error(f"خطأ في إنتاج النصائح: {str(e)}")

def display_trading_advice(advice):
    """عرض نصائح التداول"""
    
    # استراتيجية الدخول
    if 'entry_strategy' in advice:
        entry = advice['entry_strategy']
        primary_entry = entry.get('primary_entry', {})
        
        direction = primary_entry.get('direction', 'انتظار')
        
        if direction == 'شراء':
            st.markdown(f"""
            <div class="buy-signal">
                🟢 إشارة شراء - {primary_entry.get('method', 'غير محدد')}
                <br>السعر المقترح: {primary_entry.get('entry_price', 0):.2f}
                <br>مستوى الثقة: {primary_entry.get('confidence', 0):.1%}
            </div>
            """, unsafe_allow_html=True)
        elif direction == 'بيع':
            st.markdown(f"""
            <div class="sell-signal">
                🔴 إشارة بيع - {primary_entry.get('method', 'غير محدد')}
                <br>السعر المقترح: {primary_entry.get('entry_price', 0):.2f}
                <br>مستوى الثقة: {primary_entry.get('confidence', 0):.1%}
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="hold-signal">
                🟡 انتظار - {primary_entry.get('reasoning', 'مراقبة السوق')}
            </div>
            """, unsafe_allow_html=True)
    
    # تفاصيل إضافية
    col1, col2 = st.columns(2)
    
    with col1:
        if 'position_sizing' in advice:
            sizing = advice['position_sizing']
            st.markdown(f"""
            <div class="advice-box">
                <h4>💰 حجم المركز المقترح</h4>
                <p><strong>الحجم:</strong> {sizing.get('size_percentage', '0%')}</p>
                <p><strong>عدد الأسهم:</strong> {sizing.get('shares_to_buy', 0)}</p>
                <p><strong>المخاطر:</strong> {sizing.get('risk_amount', 0):.1%}</p>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        if 'exit_strategy' in advice:
            exit_strategy = advice['exit_strategy']
            stop_loss = exit_strategy.get('stop_loss', {})
            st.markdown(f"""
            <div class="advice-box">
                <h4>⛔ إدارة المخاطر</h4>
                <p><strong>وقف الخسارة:</strong> {stop_loss.get('initial_stop', 0):.2f}</p>
                <p><strong>نسبة المخاطر:</strong> {stop_loss.get('risk_percentage', 0):.1f}%</p>
                <p><strong>النوع:</strong> {stop_loss.get('type', 'غير محدد')}</p>
            </div>
            """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()

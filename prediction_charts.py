"""
مولد الرسوم البيانية للتوقعات الذكية
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import ta

class PredictionChartsGenerator:
    """
    مولد الرسوم البيانية للتوقعات
    """
    def __init__(self):
        self.colors = {
            'bullish': '#00ff88',
            'bearish': '#ff4444',
            'neutral': '#ffaa00',
            'prediction': '#00bfff',
            'confidence_high': '#32cd32',
            'confidence_medium': '#ffa500',
            'confidence_low': '#ff6347',
            'background': '#1e1e1e',
            'grid': '#333333',
            'text': '#ffffff'
        }
    
    def create_prediction_dashboard(self, data, predictions, symbol):
        """
        إنشاء لوحة تحكم شاملة للتوقعات
        """
        try:
            # إنشاء subplots
            fig = make_subplots(
                rows=3, cols=2,
                shared_xaxes=True,
                vertical_spacing=0.08,
                horizontal_spacing=0.05,
                subplot_titles=[
                    f'توقعات الأسعار - {symbol}', 'مستويات الثقة',
                    'تحليل الاتجاهات المتوقعة', 'مقارنة النماذج',
                    'نطاقات التوقعات', 'مؤشرات الأداء'
                ],
                specs=[
                    [{"secondary_y": True}, {"secondary_y": False}],
                    [{"secondary_y": False}, {"secondary_y": False}],
                    [{"colspan": 2}, None]
                ],
                row_heights=[0.4, 0.3, 0.3]
            )
            
            # الرسم الرئيسي - الأسعار والتوقعات
            self.add_price_predictions_chart(fig, data, predictions, row=1, col=1)
            
            # مستويات الثقة
            self.add_confidence_levels_chart(fig, predictions, row=1, col=2)
            
            # تحليل الاتجاهات
            self.add_trend_analysis_chart(fig, data, predictions, row=2, col=1)
            
            # مقارنة النماذج
            self.add_models_comparison_chart(fig, predictions, row=2, col=2)
            
            # نطاقات التوقعات
            self.add_prediction_ranges_chart(fig, data, predictions, row=3, col=1)
            
            # تنسيق الرسم
            self.format_prediction_chart(fig, symbol)
            
            return fig
            
        except Exception as e:
            print(f"خطأ في إنشاء لوحة التوقعات: {str(e)}")
            return None
    
    def add_price_predictions_chart(self, fig, data, predictions, row, col):
        """
        إضافة رسم الأسعار والتوقعات
        """
        try:
            # الأسعار التاريخية
            fig.add_trace(
                go.Candlestick(
                    x=data.index[-100:],  # آخر 100 نقطة
                    open=data['Open'][-100:],
                    high=data['High'][-100:],
                    low=data['Low'][-100:],
                    close=data['Close'][-100:],
                    name='الأسعار التاريخية',
                    increasing_line_color=self.colors['bullish'],
                    decreasing_line_color=self.colors['bearish']
                ),
                row=row, col=col
            )
            
            # إضافة التوقعات
            current_time = data.index[-1]
            current_price = data['Close'].iloc[-1]
            
            # نقاط التوقع المستقبلية
            future_times = []
            future_prices = []
            
            time_deltas = {
                '5min': timedelta(minutes=5),
                '10min': timedelta(minutes=10),
                '1hour': timedelta(hours=1),
                '4hour': timedelta(hours=4),
                '1day': timedelta(days=1)
            }
            
            # إضافة النقطة الحالية
            future_times.append(current_time)
            future_prices.append(current_price)
            
            # إضافة التوقعات
            for horizon, prediction in predictions.items():
                if prediction and 'predicted_price' in prediction:
                    future_time = current_time + time_deltas.get(horizon, timedelta(hours=1))
                    future_times.append(future_time)
                    future_prices.append(prediction['predicted_price'])
            
            # رسم خط التوقعات
            if len(future_times) > 1:
                fig.add_trace(
                    go.Scatter(
                        x=future_times,
                        y=future_prices,
                        mode='lines+markers',
                        name='التوقعات',
                        line=dict(color=self.colors['prediction'], width=3, dash='dash'),
                        marker=dict(size=8, color=self.colors['prediction'])
                    ),
                    row=row, col=col
                )
                
                # إضافة نطاقات الثقة
                self.add_confidence_bands(fig, future_times, future_prices, predictions, row, col)
            
            # إضافة المتوسطات المتحركة
            sma_20 = ta.trend.sma_indicator(data['Close'], window=20)
            sma_50 = ta.trend.sma_indicator(data['Close'], window=50)
            
            fig.add_trace(
                go.Scatter(
                    x=data.index[-100:],
                    y=sma_20[-100:],
                    mode='lines',
                    name='SMA 20',
                    line=dict(color='orange', width=1)
                ),
                row=row, col=col
            )
            
            fig.add_trace(
                go.Scatter(
                    x=data.index[-100:],
                    y=sma_50[-100:],
                    mode='lines',
                    name='SMA 50',
                    line=dict(color='red', width=1)
                ),
                row=row, col=col
            )
            
        except Exception as e:
            print(f"خطأ في رسم الأسعار والتوقعات: {str(e)}")
    
    def add_confidence_bands(self, fig, times, prices, predictions, row, col):
        """
        إضافة نطاقات الثقة
        """
        try:
            if len(times) < 2:
                return
            
            # حساب نطاقات الثقة
            upper_band = []
            lower_band = []
            
            for i, (time, price) in enumerate(zip(times, prices)):
                if i == 0:  # النقطة الحالية
                    upper_band.append(price)
                    lower_band.append(price)
                else:
                    # العثور على التوقع المقابل
                    horizon = None
                    for h, pred in predictions.items():
                        if pred and abs(pred.get('predicted_price', 0) - price) < 0.01:
                            horizon = h
                            break
                    
                    if horizon and predictions[horizon]:
                        confidence = predictions[horizon].get('confidence', 0.5)
                        volatility = abs(price - prices[0]) * 0.1  # تقدير التقلبات
                        
                        # نطاق الثقة بناءً على مستوى الثقة
                        band_width = volatility * (1 - confidence)
                        upper_band.append(price + band_width)
                        lower_band.append(price - band_width)
                    else:
                        upper_band.append(price)
                        lower_band.append(price)
            
            # رسم النطاقات
            fig.add_trace(
                go.Scatter(
                    x=times + times[::-1],
                    y=upper_band + lower_band[::-1],
                    fill='toself',
                    fillcolor='rgba(0, 191, 255, 0.2)',
                    line=dict(color='rgba(255,255,255,0)'),
                    name='نطاق الثقة',
                    showlegend=False
                ),
                row=row, col=col
            )
            
        except Exception as e:
            print(f"خطأ في إضافة نطاقات الثقة: {str(e)}")
    
    def add_confidence_levels_chart(self, fig, predictions, row, col):
        """
        إضافة رسم مستويات الثقة
        """
        try:
            horizons = []
            confidences = []
            colors = []
            
            for horizon, prediction in predictions.items():
                if prediction and 'confidence' in prediction:
                    horizons.append(horizon)
                    confidence = prediction['confidence']
                    confidences.append(confidence * 100)
                    
                    # تحديد اللون بناءً على مستوى الثقة
                    if confidence > 0.8:
                        colors.append(self.colors['confidence_high'])
                    elif confidence > 0.6:
                        colors.append(self.colors['confidence_medium'])
                    else:
                        colors.append(self.colors['confidence_low'])
            
            if horizons:
                fig.add_trace(
                    go.Bar(
                        x=horizons,
                        y=confidences,
                        marker_color=colors,
                        name='مستوى الثقة',
                        text=[f'{c:.1f}%' for c in confidences],
                        textposition='auto'
                    ),
                    row=row, col=col
                )
                
                # خط المرجع للثقة المقبولة
                fig.add_hline(
                    y=70, line_dash="dash", line_color="yellow",
                    annotation_text="حد الثقة المقبول (70%)",
                    row=row, col=col
                )
            
        except Exception as e:
            print(f"خطأ في رسم مستويات الثقة: {str(e)}")
    
    def add_trend_analysis_chart(self, fig, data, predictions, row, col):
        """
        إضافة تحليل الاتجاهات المتوقعة
        """
        try:
            # حساب التغيرات المتوقعة
            horizons = []
            changes = []
            colors = []
            
            current_price = data['Close'].iloc[-1]
            
            for horizon, prediction in predictions.items():
                if prediction and 'predicted_price' in prediction:
                    horizons.append(horizon)
                    change_percent = prediction.get('change_percent', 0)
                    changes.append(change_percent)
                    
                    # تحديد اللون بناءً على الاتجاه
                    if change_percent > 0:
                        colors.append(self.colors['bullish'])
                    elif change_percent < 0:
                        colors.append(self.colors['bearish'])
                    else:
                        colors.append(self.colors['neutral'])
            
            if horizons:
                fig.add_trace(
                    go.Bar(
                        x=horizons,
                        y=changes,
                        marker_color=colors,
                        name='التغير المتوقع (%)',
                        text=[f'{c:+.2f}%' for c in changes],
                        textposition='auto'
                    ),
                    row=row, col=col
                )
                
                # خط المرجع للصفر
                fig.add_hline(
                    y=0, line_dash="solid", line_color="white",
                    row=row, col=col
                )
            
        except Exception as e:
            print(f"خطأ في تحليل الاتجاهات: {str(e)}")
    
    def add_models_comparison_chart(self, fig, predictions, row, col):
        """
        إضافة مقارنة النماذج
        """
        try:
            # جمع تنبؤات النماذج المختلفة
            model_names = []
            model_predictions = []
            
            # أخذ أول توقع متاح للمقارنة
            sample_prediction = None
            for pred in predictions.values():
                if pred and 'individual_predictions' in pred:
                    sample_prediction = pred
                    break
            
            if sample_prediction:
                individual_preds = sample_prediction['individual_predictions']
                current_price = sample_prediction['current_price']
                
                for model_name, pred_price in individual_preds.items():
                    model_names.append(model_name.replace('_', ' ').title())
                    change_percent = ((pred_price - current_price) / current_price) * 100
                    model_predictions.append(change_percent)
                
                if model_names:
                    # تحديد الألوان
                    colors = [self.colors['bullish'] if p > 0 else self.colors['bearish'] if p < 0 else self.colors['neutral'] 
                             for p in model_predictions]
                    
                    fig.add_trace(
                        go.Bar(
                            x=model_names,
                            y=model_predictions,
                            marker_color=colors,
                            name='تنبؤات النماذج',
                            text=[f'{p:+.2f}%' for p in model_predictions],
                            textposition='auto'
                        ),
                        row=row, col=col
                    )
            
        except Exception as e:
            print(f"خطأ في مقارنة النماذج: {str(e)}")
    
    def add_prediction_ranges_chart(self, fig, data, predictions, row, col):
        """
        إضافة نطاقات التوقعات
        """
        try:
            current_price = data['Close'].iloc[-1]
            
            # إنشاء نطاقات للتوقعات
            horizons = []
            min_prices = []
            max_prices = []
            predicted_prices = []
            
            for horizon, prediction in predictions.items():
                if prediction and 'predicted_price' in prediction:
                    horizons.append(horizon)
                    pred_price = prediction['predicted_price']
                    confidence = prediction.get('confidence', 0.5)
                    
                    # حساب النطاق بناءً على الثقة
                    volatility = abs(pred_price - current_price) * 0.1
                    range_width = volatility * (1 - confidence)
                    
                    predicted_prices.append(pred_price)
                    min_prices.append(pred_price - range_width)
                    max_prices.append(pred_price + range_width)
            
            if horizons:
                # رسم النطاقات
                fig.add_trace(
                    go.Scatter(
                        x=horizons,
                        y=max_prices,
                        mode='lines',
                        name='الحد الأعلى',
                        line=dict(color='rgba(0,255,0,0.5)', width=1),
                        showlegend=False
                    ),
                    row=row, col=col
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=horizons,
                        y=min_prices,
                        mode='lines',
                        name='النطاق المتوقع',
                        line=dict(color='rgba(255,0,0,0.5)', width=1),
                        fill='tonexty',
                        fillcolor='rgba(0,191,255,0.2)'
                    ),
                    row=row, col=col
                )
                
                # رسم التوقعات الرئيسية
                fig.add_trace(
                    go.Scatter(
                        x=horizons,
                        y=predicted_prices,
                        mode='lines+markers',
                        name='التوقع الرئيسي',
                        line=dict(color=self.colors['prediction'], width=3),
                        marker=dict(size=8)
                    ),
                    row=row, col=col
                )
                
                # خط السعر الحالي
                fig.add_hline(
                    y=current_price,
                    line_dash="dash",
                    line_color="white",
                    annotation_text=f"السعر الحالي: {current_price:.2f}",
                    row=row, col=col
                )
            
        except Exception as e:
            print(f"خطأ في نطاقات التوقعات: {str(e)}")
    
    def format_prediction_chart(self, fig, symbol):
        """
        تنسيق رسم التوقعات
        """
        fig.update_layout(
            title={
                'text': f'🔮 لوحة التوقعات الذكية - {symbol}',
                'x': 0.5,
                'font': {'size': 24, 'color': self.colors['text']}
            },
            paper_bgcolor=self.colors['background'],
            plot_bgcolor=self.colors['background'],
            font=dict(color=self.colors['text']),
            height=1000,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        # تحديث محاور X
        fig.update_xaxes(
            gridcolor=self.colors['grid'],
            showgrid=True
        )
        
        # تحديث محاور Y
        fig.update_yaxes(
            gridcolor=self.colors['grid'],
            showgrid=True
        )
    
    def create_accuracy_tracking_chart(self, historical_predictions, actual_prices):
        """
        إنشاء رسم تتبع دقة التوقعات
        """
        try:
            fig = go.Figure()
            
            # رسم التوقعات مقابل الأسعار الفعلية
            if historical_predictions and actual_prices:
                dates = list(historical_predictions.keys())
                predicted = [pred['predicted_price'] for pred in historical_predictions.values()]
                actual = [actual_prices.get(date, 0) for date in dates]
                
                # التوقعات
                fig.add_trace(
                    go.Scatter(
                        x=dates,
                        y=predicted,
                        mode='lines+markers',
                        name='التوقعات',
                        line=dict(color=self.colors['prediction'], width=2)
                    )
                )
                
                # الأسعار الفعلية
                fig.add_trace(
                    go.Scatter(
                        x=dates,
                        y=actual,
                        mode='lines+markers',
                        name='الأسعار الفعلية',
                        line=dict(color=self.colors['bullish'], width=2)
                    )
                )
                
                # حساب الدقة
                errors = [abs(p - a) / a * 100 for p, a in zip(predicted, actual) if a != 0]
                avg_error = np.mean(errors) if errors else 0
                accuracy = max(0, 100 - avg_error)
                
                fig.add_annotation(
                    text=f"دقة التوقعات: {accuracy:.1f}%",
                    xref="paper", yref="paper",
                    x=0.02, y=0.98,
                    showarrow=False,
                    font=dict(size=16, color=self.colors['text']),
                    bgcolor="rgba(0,0,0,0.5)",
                    bordercolor=self.colors['text']
                )
            
            fig.update_layout(
                title='📊 تتبع دقة التوقعات',
                paper_bgcolor=self.colors['background'],
                plot_bgcolor=self.colors['background'],
                font=dict(color=self.colors['text']),
                height=400
            )
            
            return fig
            
        except Exception as e:
            print(f"خطأ في رسم تتبع الدقة: {str(e)}")
            return None
    
    def create_prediction_summary_table(self, predictions):
        """
        إنشاء جدول ملخص التوقعات
        """
        try:
            if not predictions:
                return None
            
            # إعداد البيانات للجدول
            horizons = []
            predicted_prices = []
            changes = []
            confidences = []
            directions = []
            
            for horizon, prediction in predictions.items():
                if prediction:
                    horizons.append(horizon)
                    predicted_prices.append(f"{prediction.get('predicted_price', 0):.2f}")
                    change_percent = prediction.get('change_percent', 0)
                    changes.append(f"{change_percent:+.2f}%")
                    confidences.append(f"{prediction.get('confidence', 0)*100:.1f}%")
                    
                    if change_percent > 0.5:
                        directions.append("🟢 صاعد")
                    elif change_percent < -0.5:
                        directions.append("🔴 هابط")
                    else:
                        directions.append("🟡 جانبي")
            
            # إنشاء الجدول
            fig = go.Figure(data=[go.Table(
                header=dict(
                    values=['الأفق الزمني', 'السعر المتوقع', 'التغير المتوقع', 'مستوى الثقة', 'الاتجاه'],
                    fill_color='darkblue',
                    font=dict(color='white', size=14),
                    align='center'
                ),
                cells=dict(
                    values=[horizons, predicted_prices, changes, confidences, directions],
                    fill_color='lightgray',
                    font=dict(color='black', size=12),
                    align='center'
                )
            )])
            
            fig.update_layout(
                title='📋 ملخص التوقعات',
                height=300,
                margin=dict(l=0, r=0, t=30, b=0)
            )
            
            return fig
            
        except Exception as e:
            print(f"خطأ في إنشاء جدول التوقعات: {str(e)}")
            return None

# مثال على الاستخدام
if __name__ == "__main__":
    print("📊 مولد الرسوم البيانية للتوقعات جاهز!")

"""
محركات التحليل المتقدمة للذكاء الاصطناعي الفائق
"""

import numpy as np
import pandas as pd
from scipy import signal, stats
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class FractalAnalyzer:
    """
    محلل الفراكتال المتقدم
    """
    def __init__(self):
        self.scaler = StandardScaler()
    
    def calculate_dimension(self, prices):
        """
        حساب البعد الفراكتالي باستخدام طريقة Higuchi
        """
        try:
            N = len(prices)
            if N < 10:
                return 1.5
            
            k_max = min(20, N // 4)
            L_k = []
            
            for k in range(1, k_max + 1):
                L_m = []
                for m in range(k):
                    L_m_k = 0
                    max_i = int((N - m - 1) / k)
                    
                    if max_i > 0:
                        for i in range(1, max_i + 1):
                            L_m_k += abs(prices[m + i * k] - prices[m + (i - 1) * k])
                        
                        L_m_k = L_m_k * (N - 1) / (max_i * k * k)
                        L_m.append(L_m_k)
                
                if L_m:
                    L_k.append(np.mean(L_m))
            
            if len(L_k) < 2:
                return 1.5
            
            # حساب البعد الفراكتالي
            k_values = np.arange(1, len(L_k) + 1)
            log_k = np.log(k_values)
            log_L = np.log(L_k)
            
            # الانحدار الخطي
            slope, _, _, _, _ = stats.linregress(log_k, log_L)
            fractal_dimension = -slope
            
            return max(1.0, min(2.0, fractal_dimension))
            
        except Exception as e:
            return 1.5
    
    def analyze_structure(self, prices):
        """
        تحليل الهيكل الفراكتالي
        """
        try:
            dimension = self.calculate_dimension(prices)
            
            # تحليل التعقيد
            if dimension > 1.7:
                complexity = "معقد جداً"
                market_state = "عشوائي"
            elif dimension > 1.5:
                complexity = "معقد"
                market_state = "متقلب"
            elif dimension > 1.3:
                complexity = "متوسط"
                market_state = "متوازن"
            else:
                complexity = "بسيط"
                market_state = "اتجاهي"
            
            # تحليل الاستمرارية
            persistence = self.calculate_hurst_exponent(prices)
            
            return {
                'complexity': complexity,
                'market_state': market_state,
                'dimension': dimension,
                'persistence': persistence,
                'predictability': 'عالي' if persistence > 0.6 or persistence < 0.4 else 'منخفض'
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_hurst_exponent(self, prices):
        """
        حساب أس هيرست لقياس الاستمرارية
        """
        try:
            returns = np.diff(np.log(prices))
            N = len(returns)
            
            if N < 20:
                return 0.5
            
            # حساب المتوسط التراكمي
            mean_return = np.mean(returns)
            cumulative_deviations = np.cumsum(returns - mean_return)
            
            # حساب المدى
            R = np.max(cumulative_deviations) - np.min(cumulative_deviations)
            
            # حساب الانحراف المعياري
            S = np.std(returns)
            
            if S == 0:
                return 0.5
            
            # نسبة R/S
            rs_ratio = R / S
            
            # تقدير أس هيرست
            if rs_ratio > 0:
                hurst = np.log(rs_ratio) / np.log(N)
            else:
                hurst = 0.5
            
            return max(0.0, min(1.0, hurst))
            
        except Exception as e:
            return 0.5

class ElliottWaveAnalyzer:
    """
    محلل موجات إليوت المتقدم
    """
    def __init__(self):
        self.wave_patterns = {
            'impulse': [1, 2, 3, 4, 5],
            'corrective': ['A', 'B', 'C']
        }
    
    def detect_waves(self, prices):
        """
        اكتشاف موجات إليوت
        """
        try:
            # العثور على القمم والقيعان
            peaks, valleys = self.find_peaks_valleys(prices)
            
            # تحليل الموجات
            waves = self.analyze_wave_structure(prices, peaks, valleys)
            
            return waves
            
        except Exception as e:
            return []
    
    def find_peaks_valleys(self, prices):
        """
        العثور على القمم والقيعان
        """
        try:
            # استخدام scipy لإيجاد القمم
            peaks, _ = signal.find_peaks(prices, distance=5, prominence=np.std(prices) * 0.5)
            valleys, _ = signal.find_peaks(-prices, distance=5, prominence=np.std(prices) * 0.5)
            
            return peaks, valleys
            
        except Exception as e:
            return [], []
    
    def analyze_wave_structure(self, prices, peaks, valleys):
        """
        تحليل هيكل الموجات
        """
        try:
            waves = []
            
            # دمج القمم والقيعان وترتيبها
            all_points = []
            for peak in peaks:
                all_points.append((peak, prices[peak], 'peak'))
            for valley in valleys:
                all_points.append((valley, prices[valley], 'valley'))
            
            # ترتيب حسب الفهرس
            all_points.sort(key=lambda x: x[0])
            
            # تحليل الموجات
            if len(all_points) >= 5:
                for i in range(len(all_points) - 4):
                    wave_segment = all_points[i:i+5]
                    wave_type = self.classify_wave_pattern(wave_segment)
                    
                    waves.append({
                        'start_index': wave_segment[0][0],
                        'end_index': wave_segment[-1][0],
                        'type': wave_type,
                        'points': wave_segment
                    })
            
            return waves
            
        except Exception as e:
            return []
    
    def classify_wave_pattern(self, wave_segment):
        """
        تصنيف نمط الموجة
        """
        try:
            # تحليل بسيط لنمط الموجة
            if len(wave_segment) < 5:
                return 'غير مكتمل'
            
            # فحص إذا كانت موجة دافعة أم تصحيحية
            price_changes = []
            for i in range(1, len(wave_segment)):
                change = wave_segment[i][1] - wave_segment[i-1][1]
                price_changes.append(change)
            
            # إذا كانت الحركة الإجمالية صاعدة
            total_change = sum(price_changes)
            
            if total_change > 0:
                return 'موجة دافعة صاعدة'
            elif total_change < 0:
                return 'موجة دافعة هابطة'
            else:
                return 'موجة تصحيحية'
                
        except Exception as e:
            return 'غير محدد'
    
    def identify_current_wave(self, waves, prices):
        """
        تحديد الموجة الحالية
        """
        try:
            if not waves:
                return 'غير محدد'
            
            # أخذ آخر موجة
            current_wave = waves[-1]
            current_position = len(prices) - 1
            
            # تحديد موقع السعر الحالي في الموجة
            if current_position >= current_wave['end_index']:
                return f"بعد {current_wave['type']}"
            elif current_position >= current_wave['start_index']:
                return f"داخل {current_wave['type']}"
            else:
                return 'غير محدد'
                
        except Exception as e:
            return 'غير محدد'
    
    def predict_next_wave(self, current_wave, prices):
        """
        التنبؤ بالموجة التالية
        """
        try:
            if current_wave == 'غير محدد':
                return 'غير محدد'
            
            # منطق بسيط للتنبؤ
            if 'دافعة صاعدة' in current_wave:
                return 'موجة تصحيحية هابطة متوقعة'
            elif 'دافعة هابطة' in current_wave:
                return 'موجة تصحيحية صاعدة متوقعة'
            elif 'تصحيحية' in current_wave:
                return 'موجة دافعة متوقعة'
            else:
                return 'غير محدد'
                
        except Exception as e:
            return 'غير محدد'

class GannAnalyzer:
    """
    محلل جان المتقدم
    """
    def __init__(self):
        self.gann_angles = [1, 2, 3, 4, 8]  # زوايا جان الأساسية
    
    def calculate_gann_angles(self, prices, times):
        """
        حساب زوايا جان
        """
        try:
            angles = {}
            
            # العثور على نقاط مهمة
            significant_points = self.find_significant_points(prices)
            
            for point_idx in significant_points:
                point_price = prices[point_idx]
                point_time = point_idx
                
                # حساب الزوايا من هذه النقطة
                for angle in self.gann_angles:
                    angle_line = self.calculate_angle_line(
                        point_time, point_price, angle, len(prices)
                    )
                    angles[f'angle_{angle}_from_{point_idx}'] = angle_line
            
            return angles
            
        except Exception as e:
            return {}
    
    def find_significant_points(self, prices):
        """
        العثور على النقاط المهمة (قمم وقيعان رئيسية)
        """
        try:
            # استخدام تحليل بسيط للعثور على النقاط المهمة
            window = max(10, len(prices) // 20)
            significant_points = []
            
            for i in range(window, len(prices) - window):
                # فحص إذا كانت قمة محلية
                if all(prices[i] >= prices[j] for j in range(i - window, i + window + 1)):
                    significant_points.append(i)
                # فحص إذا كانت قاع محلي
                elif all(prices[i] <= prices[j] for j in range(i - window, i + window + 1)):
                    significant_points.append(i)
            
            return significant_points[:5]  # أخذ أول 5 نقاط
            
        except Exception as e:
            return []
    
    def calculate_angle_line(self, start_time, start_price, angle, length):
        """
        حساب خط الزاوية
        """
        try:
            line_points = []
            
            # حساب الميل بناءً على الزاوية
            slope = angle / 100  # تبسيط
            
            for t in range(start_time, min(start_time + length, length)):
                price = start_price + slope * (t - start_time)
                line_points.append((t, price))
            
            return line_points
            
        except Exception as e:
            return []

class WyckoffAnalyzer:
    """
    محلل ويكوف المتقدم
    """
    def __init__(self):
        self.phases = ['تراكم', 'ترقية', 'توزيع', 'تراجع']
    
    def identify_wyckoff_phases(self, prices, volumes):
        """
        تحديد مراحل ويكوف
        """
        try:
            phases = []
            
            # تحليل العلاقة بين السعر والحجم
            price_changes = np.diff(prices)
            volume_changes = np.diff(volumes)
            
            # تقسيم البيانات إلى فترات
            window_size = max(20, len(prices) // 10)
            
            for i in range(0, len(prices) - window_size, window_size):
                window_prices = prices[i:i + window_size]
                window_volumes = volumes[i:i + window_size]
                
                phase = self.analyze_phase(window_prices, window_volumes)
                phases.append({
                    'start': i,
                    'end': i + window_size,
                    'phase': phase
                })
            
            return phases
            
        except Exception as e:
            return []
    
    def analyze_phase(self, prices, volumes):
        """
        تحليل المرحلة الحالية
        """
        try:
            # حساب الارتباط بين السعر والحجم
            correlation = np.corrcoef(prices, volumes)[0, 1]
            
            # تحليل الاتجاه
            price_trend = np.polyfit(range(len(prices)), prices, 1)[0]
            volume_trend = np.polyfit(range(len(volumes)), volumes, 1)[0]
            
            # تحديد المرحلة
            if price_trend > 0 and volume_trend > 0:
                return 'ترقية'
            elif price_trend < 0 and volume_trend > 0:
                return 'توزيع'
            elif price_trend > 0 and volume_trend < 0:
                return 'تراكم'
            else:
                return 'تراجع'
                
        except Exception as e:
            return 'غير محدد'
    
    def wyckoff_accumulation_distribution(self, prices, volumes):
        """
        تحليل التراكم والتوزيع
        """
        try:
            # حساب مؤشر التراكم/التوزيع
            ad_line = []
            ad_value = 0
            
            for i in range(1, len(prices)):
                if prices[i] != prices[i-1]:
                    money_flow_multiplier = ((prices[i] - prices[i-1]) / 
                                           (max(prices[i], prices[i-1]) - min(prices[i], prices[i-1])))
                    money_flow_volume = money_flow_multiplier * volumes[i]
                    ad_value += money_flow_volume
                
                ad_line.append(ad_value)
            
            # تحليل الاتجاه
            if len(ad_line) > 10:
                recent_trend = np.polyfit(range(len(ad_line[-10:])), ad_line[-10:], 1)[0]
                
                if recent_trend > 0:
                    return 'تراكم'
                elif recent_trend < 0:
                    return 'توزيع'
                else:
                    return 'متوازن'
            
            return 'غير محدد'
            
        except Exception as e:
            return 'غير محدد'
    
    def wyckoff_strength_weakness(self, prices, volumes):
        """
        تحديد نقاط القوة والضعف
        """
        try:
            strength_signals = []
            weakness_signals = []
            
            for i in range(1, len(prices)):
                price_change = prices[i] - prices[i-1]
                volume_ratio = volumes[i] / np.mean(volumes[max(0, i-10):i]) if i > 10 else 1
                
                # إشارات القوة
                if price_change > 0 and volume_ratio > 1.5:
                    strength_signals.append(i)
                
                # إشارات الضعف
                elif price_change < 0 and volume_ratio > 1.5:
                    weakness_signals.append(i)
            
            return {
                'strength_points': strength_signals,
                'weakness_points': weakness_signals,
                'overall_sentiment': 'قوي' if len(strength_signals) > len(weakness_signals) else 'ضعيف'
            }
            
        except Exception as e:
            return {'error': str(e)}

class MarketProfileAnalyzer:
    """
    محلل الملف الشخصي للسوق
    """
    def __init__(self):
        pass
    
    def calculate_volume_poc(self, prices, volumes):
        """
        حساب نقطة التحكم في الحجم
        """
        try:
            # تجميع الحجم حسب مستويات الأسعار
            price_levels = {}
            
            for price, volume in zip(prices, volumes):
                price_level = round(price, 2)
                if price_level in price_levels:
                    price_levels[price_level] += volume
                else:
                    price_levels[price_level] = volume
            
            # العثور على المستوى بأعلى حجم
            poc_price = max(price_levels, key=price_levels.get)
            poc_volume = price_levels[poc_price]
            
            return {
                'price': poc_price,
                'volume': poc_volume,
                'percentage': poc_volume / sum(volumes) * 100
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_value_area(self, prices, volumes):
        """
        حساب منطقة القيمة (70% من الحجم)
        """
        try:
            # تجميع الحجم حسب مستويات الأسعار
            price_levels = {}
            
            for price, volume in zip(prices, volumes):
                price_level = round(price, 2)
                if price_level in price_levels:
                    price_levels[price_level] += volume
                else:
                    price_levels[price_level] = volume
            
            # ترتيب حسب الحجم
            sorted_levels = sorted(price_levels.items(), key=lambda x: x[1], reverse=True)
            
            total_volume = sum(volumes)
            target_volume = total_volume * 0.7
            
            # العثور على منطقة القيمة
            accumulated_volume = 0
            value_area_prices = []
            
            for price, volume in sorted_levels:
                accumulated_volume += volume
                value_area_prices.append(price)
                
                if accumulated_volume >= target_volume:
                    break
            
            return {
                'high': max(value_area_prices),
                'low': min(value_area_prices),
                'prices': value_area_prices
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_volume_distribution(self, prices, volumes):
        """
        تحليل توزيع الحجم
        """
        try:
            # حساب الإحصائيات الأساسية
            mean_volume = np.mean(volumes)
            std_volume = np.std(volumes)
            
            # تصنيف الحجم
            high_volume_threshold = mean_volume + std_volume
            low_volume_threshold = mean_volume - std_volume
            
            high_volume_count = sum(1 for v in volumes if v > high_volume_threshold)
            low_volume_count = sum(1 for v in volumes if v < low_volume_threshold)
            
            return {
                'mean_volume': mean_volume,
                'std_volume': std_volume,
                'high_volume_sessions': high_volume_count,
                'low_volume_sessions': low_volume_count,
                'distribution_type': 'متوازن' if abs(high_volume_count - low_volume_count) < 5 else 'غير متوازن'
            }
            
        except Exception as e:
            return {'error': str(e)}

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء بيانات تجريبية
    prices = np.random.random(100) * 100 + 100
    volumes = np.random.random(100) * 1000 + 500
    
    # اختبار المحللات
    fractal_analyzer = FractalAnalyzer()
    elliott_analyzer = ElliottWaveAnalyzer()
    
    print("🔍 اختبار محركات التحليل المتقدمة...")
    
    # تحليل الفراكتال
    fractal_result = fractal_analyzer.analyze_structure(prices)
    print(f"تحليل الفراكتال: {fractal_result}")
    
    # تحليل موجات إليوت
    waves = elliott_analyzer.detect_waves(prices)
    print(f"موجات إليوت: {len(waves)} موجة مكتشفة")
    
    print("✅ تم اختبار المحركات بنجاح!")

"""
وظائف مساعدة للتطبيق
"""

import pandas as pd
import numpy as np
import os
import json
import logging
from datetime import datetime, timedelta
import yfinance as yf
from config import PATHS

def setup_logging():
    """
    إعداد نظام السجلات
    """
    log_dir = PATHS['logs']
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"stock_predictor_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def validate_symbol(symbol):
    """
    التحقق من صحة رمز السهم
    """
    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info
        
        # التحقق من وجود بيانات أساسية
        if 'regularMarketPrice' in info or 'currentPrice' in info:
            return True
        else:
            return False
            
    except Exception:
        return False

def calculate_returns(prices):
    """
    حساب العوائد
    """
    try:
        if len(prices) < 2:
            return np.array([])
        
        returns = np.diff(prices) / prices[:-1]
        return returns
        
    except Exception as e:
        print(f"خطأ في حساب العوائد: {str(e)}")
        return np.array([])

def calculate_volatility(prices, window=20):
    """
    حساب التقلبات
    """
    try:
        returns = calculate_returns(prices)
        if len(returns) < window:
            return np.nan
        
        volatility = np.std(returns[-window:]) * np.sqrt(252)  # تقلبات سنوية
        return volatility
        
    except Exception as e:
        print(f"خطأ في حساب التقلبات: {str(e)}")
        return np.nan

def calculate_sharpe_ratio(prices, risk_free_rate=0.02):
    """
    حساب نسبة شارب
    """
    try:
        returns = calculate_returns(prices)
        if len(returns) == 0:
            return np.nan
        
        excess_returns = np.mean(returns) * 252 - risk_free_rate
        volatility = np.std(returns) * np.sqrt(252)
        
        if volatility == 0:
            return np.nan
        
        sharpe_ratio = excess_returns / volatility
        return sharpe_ratio
        
    except Exception as e:
        print(f"خطأ في حساب نسبة شارب: {str(e)}")
        return np.nan

def calculate_max_drawdown(prices):
    """
    حساب أقصى انخفاض
    """
    try:
        if len(prices) < 2:
            return np.nan
        
        cumulative = np.cumprod(1 + calculate_returns(prices))
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        return max_drawdown
        
    except Exception as e:
        print(f"خطأ في حساب أقصى انخفاض: {str(e)}")
        return np.nan

def format_number(number, decimal_places=2):
    """
    تنسيق الأرقام
    """
    try:
        if pd.isna(number):
            return "N/A"
        
        if abs(number) >= 1e9:
            return f"{number/1e9:.{decimal_places}f}B"
        elif abs(number) >= 1e6:
            return f"{number/1e6:.{decimal_places}f}M"
        elif abs(number) >= 1e3:
            return f"{number/1e3:.{decimal_places}f}K"
        else:
            return f"{number:.{decimal_places}f}"
            
    except Exception:
        return "N/A"

def save_predictions_history(symbol, predictions, confidence):
    """
    حفظ تاريخ التنبؤات
    """
    try:
        history_file = os.path.join(PATHS['data'], f"{symbol}_predictions_history.json")
        
        # تحميل التاريخ الموجود
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
        else:
            history = []
        
        # إضافة التنبؤ الجديد
        new_prediction = {
            'timestamp': datetime.now().isoformat(),
            'predictions': predictions,
            'confidence': confidence
        }
        
        history.append(new_prediction)
        
        # الاحتفاظ بآخر 100 تنبؤ فقط
        if len(history) > 100:
            history = history[-100:]
        
        # حفظ التاريخ المحدث
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        print(f"خطأ في حفظ تاريخ التنبؤات: {str(e)}")
        return False

def load_predictions_history(symbol):
    """
    تحميل تاريخ التنبؤات
    """
    try:
        history_file = os.path.join(PATHS['data'], f"{symbol}_predictions_history.json")
        
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            return history
        else:
            return []
            
    except Exception as e:
        print(f"خطأ في تحميل تاريخ التنبؤات: {str(e)}")
        return []

def calculate_prediction_accuracy(symbol, days_back=30):
    """
    حساب دقة التنبؤات السابقة
    """
    try:
        history = load_predictions_history(symbol)
        if not history:
            return None
        
        # جلب الأسعار الفعلية
        collector = DataCollector()
        actual_data = collector.fetch_stock_data(symbol, period='3mo')
        
        if actual_data is None:
            return None
        
        accuracies = []
        
        for prediction in history[-days_back:]:
            pred_date = datetime.fromisoformat(prediction['timestamp'])
            
            # البحث عن السعر الفعلي في اليوم التالي
            next_day = pred_date + timedelta(days=1)
            
            # العثور على أقرب تاريخ تداول
            closest_date = None
            min_diff = timedelta.max
            
            for date in actual_data.index:
                diff = abs(date.to_pydatetime() - next_day)
                if diff < min_diff:
                    min_diff = diff
                    closest_date = date
            
            if closest_date and min_diff <= timedelta(days=3):
                actual_price = actual_data.loc[closest_date, 'Close']
                
                if 'Ensemble' in prediction['predictions']:
                    predicted_price = prediction['predictions']['Ensemble']
                    error = abs(predicted_price - actual_price) / actual_price
                    accuracy = max(0, 1 - error)
                    accuracies.append(accuracy)
        
        if accuracies:
            return np.mean(accuracies)
        else:
            return None
            
    except Exception as e:
        print(f"خطأ في حساب دقة التنبؤات: {str(e)}")
        return None

def get_market_sentiment(symbol):
    """
    تحليل معنويات السوق (مبسط)
    """
    try:
        # جلب بيانات قصيرة المدى
        ticker = yf.Ticker(symbol)
        data = ticker.history(period='1mo')
        
        if data.empty:
            return "محايد"
        
        # حساب التغيير في الأسعار
        recent_change = (data['Close'].iloc[-1] - data['Close'].iloc[0]) / data['Close'].iloc[0]
        
        # حساب التقلبات
        volatility = data['Close'].pct_change().std()
        
        # تحديد المعنويات
        if recent_change > 0.05 and volatility < 0.03:
            return "إيجابي قوي"
        elif recent_change > 0.02:
            return "إيجابي"
        elif recent_change < -0.05 and volatility > 0.04:
            return "سلبي قوي"
        elif recent_change < -0.02:
            return "سلبي"
        else:
            return "محايد"
            
    except Exception as e:
        print(f"خطأ في تحليل معنويات السوق: {str(e)}")
        return "غير محدد"

def clean_old_files(days_old=30):
    """
    تنظيف الملفات القديمة
    """
    try:
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(days=days_old)
        
        for path in PATHS.values():
            if os.path.exists(path):
                for filename in os.listdir(path):
                    file_path = os.path.join(path, filename)
                    
                    if os.path.isfile(file_path):
                        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        if file_time < cutoff_time:
                            os.remove(file_path)
                            print(f"تم حذف الملف القديم: {filename}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في تنظيف الملفات: {str(e)}")
        return False

def export_predictions_to_csv(symbol, predictions, filename=None):
    """
    تصدير التنبؤات إلى ملف CSV
    """
    try:
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{symbol}_predictions_{timestamp}.csv"
        
        filepath = os.path.join(PATHS['data'], filename)
        
        # تحويل التنبؤات إلى DataFrame
        df = pd.DataFrame([predictions])
        df['Symbol'] = symbol
        df['Timestamp'] = datetime.now()
        
        # حفظ الملف
        df.to_csv(filepath, index=False)
        
        print(f"تم تصدير التنبؤات إلى: {filepath}")
        return filepath
        
    except Exception as e:
        print(f"خطأ في تصدير التنبؤات: {str(e)}")
        return None

def get_system_info():
    """
    الحصول على معلومات النظام
    """
    try:
        import psutil
        import platform
        
        info = {
            'platform': platform.system(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
            'memory_available': psutil.virtual_memory().available / (1024**3),  # GB
            'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
        }
        
        return info
        
    except ImportError:
        return {'error': 'psutil not installed'}
    except Exception as e:
        return {'error': str(e)}

# استيراد DataCollector هنا لتجنب الاستيراد الدائري
from data_collector import DataCollector

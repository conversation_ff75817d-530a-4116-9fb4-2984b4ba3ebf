"""
نظام إدارة المخاطر المتقدم
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.covariance import EmpiricalCovariance
import warnings
warnings.filterwarnings('ignore')

class RiskAssessment:
    """
    تقييم المخاطر المتقدم
    """
    def __init__(self):
        self.risk_models = {
            'var': self.calculate_var,
            'cvar': self.calculate_cvar,
            'maximum_drawdown': self.calculate_max_drawdown,
            'volatility': self.calculate_volatility,
            'correlation_risk': self.calculate_correlation_risk
        }
    
    def assess_risk(self, market_data, analysis_data):
        """
        تقييم شامل للمخاطر
        """
        try:
            risk_metrics = {}
            
            # استخراج البيانات
            if 'Close' in market_data:
                prices = market_data['Close']
                returns = np.diff(np.log(prices))
                
                # حساب مقاييس المخاطر المختلفة
                for risk_type, calculator in self.risk_models.items():
                    try:
                        risk_metrics[risk_type] = calculator(returns, prices)
                    except Exception as e:
                        risk_metrics[risk_type] = 0.5
                
                # حساب المخاطر الإجمالية
                overall_risk = self.calculate_overall_risk(risk_metrics)
                risk_metrics['overall_risk'] = overall_risk
                
                # تصنيف مستوى المخاطر
                risk_metrics['risk_level'] = self.classify_risk_level(overall_risk)
                
                # توصيات إدارة المخاطر
                risk_metrics['recommendations'] = self.generate_risk_recommendations(risk_metrics)
            
            return risk_metrics
            
        except Exception as e:
            return {'error': str(e), 'overall_risk': 0.5}
    
    def calculate_var(self, returns, prices, confidence=0.05):
        """
        حساب Value at Risk
        """
        try:
            if len(returns) < 10:
                return 0.02
            
            var_value = np.percentile(returns, confidence * 100)
            return abs(var_value)
            
        except Exception as e:
            return 0.02
    
    def calculate_cvar(self, returns, prices, confidence=0.05):
        """
        حساب Conditional Value at Risk
        """
        try:
            if len(returns) < 10:
                return 0.03
            
            var_threshold = np.percentile(returns, confidence * 100)
            tail_losses = returns[returns <= var_threshold]
            
            if len(tail_losses) > 0:
                cvar_value = np.mean(tail_losses)
                return abs(cvar_value)
            else:
                return abs(var_threshold)
                
        except Exception as e:
            return 0.03
    
    def calculate_max_drawdown(self, returns, prices):
        """
        حساب أقصى انخفاض
        """
        try:
            if len(prices) < 2:
                return 0.05
            
            # حساب القيم التراكمية
            cumulative = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative)
            
            # حساب الانخفاض
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = np.min(drawdown)
            
            return abs(max_drawdown)
            
        except Exception as e:
            return 0.05
    
    def calculate_volatility(self, returns, prices):
        """
        حساب التقلبات
        """
        try:
            if len(returns) < 2:
                return 0.02
            
            # التقلبات السنوية
            volatility = np.std(returns) * np.sqrt(252)
            return volatility
            
        except Exception as e:
            return 0.02
    
    def calculate_correlation_risk(self, returns, prices):
        """
        حساب مخاطر الارتباط
        """
        try:
            # مخاطر الارتباط مع السوق (مبسط)
            # في التطبيق الحقيقي، نحتاج بيانات السوق
            return 0.3
            
        except Exception as e:
            return 0.3
    
    def calculate_overall_risk(self, risk_metrics):
        """
        حساب المخاطر الإجمالية
        """
        try:
            weights = {
                'var': 0.25,
                'cvar': 0.25,
                'maximum_drawdown': 0.20,
                'volatility': 0.20,
                'correlation_risk': 0.10
            }
            
            weighted_risk = 0
            total_weight = 0
            
            for metric, weight in weights.items():
                if metric in risk_metrics:
                    weighted_risk += risk_metrics[metric] * weight
                    total_weight += weight
            
            if total_weight > 0:
                return weighted_risk / total_weight
            else:
                return 0.5
                
        except Exception as e:
            return 0.5
    
    def classify_risk_level(self, overall_risk):
        """
        تصنيف مستوى المخاطر
        """
        if overall_risk < 0.2:
            return 'منخفض'
        elif overall_risk < 0.4:
            return 'متوسط منخفض'
        elif overall_risk < 0.6:
            return 'متوسط'
        elif overall_risk < 0.8:
            return 'مرتفع'
        else:
            return 'مرتفع جداً'
    
    def generate_risk_recommendations(self, risk_metrics):
        """
        إنتاج توصيات إدارة المخاطر
        """
        recommendations = []
        
        try:
            overall_risk = risk_metrics.get('overall_risk', 0.5)
            volatility = risk_metrics.get('volatility', 0.02)
            max_drawdown = risk_metrics.get('maximum_drawdown', 0.05)
            
            # توصيات بناءً على التقلبات
            if volatility > 0.4:
                recommendations.append("تقلبات عالية - قلل حجم المركز")
            elif volatility < 0.1:
                recommendations.append("تقلبات منخفضة - يمكن زيادة حجم المركز")
            
            # توصيات بناءً على أقصى انخفاض
            if max_drawdown > 0.2:
                recommendations.append("مخاطر انخفاض عالية - استخدم وقف خسارة ضيق")
            
            # توصيات عامة
            if overall_risk > 0.7:
                recommendations.append("مخاطر مرتفعة - فكر في تأجيل التداول")
            elif overall_risk < 0.3:
                recommendations.append("مخاطر منخفضة - فرصة جيدة للدخول")
            
            return recommendations
            
        except Exception as e:
            return ["استخدم إدارة مخاطر محافظة"]

class PositionSizing:
    """
    تحديد حجم المركز الأمثل
    """
    def __init__(self):
        self.sizing_methods = {
            'kelly': self.kelly_criterion,
            'fixed_fractional': self.fixed_fractional,
            'volatility_adjusted': self.volatility_adjusted,
            'risk_parity': self.risk_parity
        }
    
    def calculate_optimal_size(self, risk_assessment, config):
        """
        حساب حجم المركز الأمثل
        """
        try:
            overall_risk = risk_assessment.get('overall_risk', 0.5)
            volatility = risk_assessment.get('volatility', 0.02)
            
            # حساب الحجم بطرق مختلفة
            sizes = {}
            
            for method, calculator in self.sizing_methods.items():
                try:
                    sizes[method] = calculator(risk_assessment, config)
                except Exception as e:
                    sizes[method] = 0.02
            
            # اختيار الحجم الأمثل
            optimal_size = self.select_optimal_size(sizes, risk_assessment)
            
            return {
                'percentage': f"{optimal_size * 100:.1f}%",
                'decimal': optimal_size,
                'method': 'متكيف مع المخاطر',
                'max_loss': f"{optimal_size * risk_assessment.get('var', 0.02) * 100:.1f}%",
                'recommendations': self.generate_sizing_recommendations(optimal_size, risk_assessment)
            }
            
        except Exception as e:
            return {'percentage': '2%', 'decimal': 0.02}
    
    def kelly_criterion(self, risk_assessment, config):
        """
        معيار كيلي لتحديد الحجم
        """
        try:
            # تقدير احتمالية النجاح والعائد
            win_rate = 0.6  # افتراضي
            avg_win = 0.03  # افتراضي
            avg_loss = 0.02  # افتراضي
            
            if avg_loss > 0:
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                return max(0.01, min(0.25, kelly_fraction))
            else:
                return 0.02
                
        except Exception as e:
            return 0.02
    
    def fixed_fractional(self, risk_assessment, config):
        """
        الكسر الثابت
        """
        try:
            base_fraction = 0.02
            risk_multiplier = 1 - risk_assessment.get('overall_risk', 0.5)
            return base_fraction * risk_multiplier
            
        except Exception as e:
            return 0.02
    
    def volatility_adjusted(self, risk_assessment, config):
        """
        التعديل حسب التقلبات
        """
        try:
            target_volatility = 0.15
            actual_volatility = risk_assessment.get('volatility', 0.02)
            
            if actual_volatility > 0:
                size = target_volatility / actual_volatility * 0.02
                return max(0.005, min(0.1, size))
            else:
                return 0.02
                
        except Exception as e:
            return 0.02
    
    def risk_parity(self, risk_assessment, config):
        """
        تكافؤ المخاطر
        """
        try:
            # تحديد الحجم بناءً على مساهمة المخاطر
            target_risk_contribution = 0.05
            position_risk = risk_assessment.get('var', 0.02)
            
            if position_risk > 0:
                size = target_risk_contribution / position_risk
                return max(0.01, min(0.2, size))
            else:
                return 0.02
                
        except Exception as e:
            return 0.02
    
    def select_optimal_size(self, sizes, risk_assessment):
        """
        اختيار الحجم الأمثل
        """
        try:
            # أخذ متوسط الطرق مع تعديل للمخاطر
            avg_size = np.mean(list(sizes.values()))
            
            # تعديل بناءً على مستوى المخاطر
            risk_factor = 1 - risk_assessment.get('overall_risk', 0.5) * 0.5
            
            optimal_size = avg_size * risk_factor
            
            return max(0.005, min(0.1, optimal_size))
            
        except Exception as e:
            return 0.02
    
    def generate_sizing_recommendations(self, size, risk_assessment):
        """
        إنتاج توصيات حجم المركز
        """
        recommendations = []
        
        try:
            if size > 0.05:
                recommendations.append("حجم مركز كبير - تأكد من إدارة المخاطر")
            elif size < 0.01:
                recommendations.append("حجم مركز صغير - قد تحتاج لزيادة التعرض")
            
            overall_risk = risk_assessment.get('overall_risk', 0.5)
            if overall_risk > 0.6:
                recommendations.append("مخاطر مرتفعة - فكر في تقليل الحجم أكثر")
            
            return recommendations
            
        except Exception as e:
            return ["استخدم حجم مركز محافظ"]

class TimingOptimizer:
    """
    محسن التوقيت
    """
    def __init__(self):
        pass
    
    def optimize_entry_exit(self, market_data, signals, analysis_data):
        """
        تحسين توقيت الدخول والخروج
        """
        try:
            timing = {
                'optimal_entry': self.find_optimal_entry(market_data, signals),
                'optimal_exit': self.find_optimal_exit(market_data, signals),
                'market_timing_score': self.calculate_market_timing_score(market_data),
                'intraday_patterns': self.analyze_intraday_patterns(market_data),
                'recommendations': []
            }
            
            # إضافة التوصيات
            timing['recommendations'] = self.generate_timing_recommendations(timing)
            
            return timing
            
        except Exception as e:
            return {'optimal_entry': 'فوري', 'optimal_exit': 'حسب الأهداف'}
    
    def find_optimal_entry(self, market_data, signals):
        """
        العثور على أفضل توقيت للدخول
        """
        try:
            # تحليل بسيط للتوقيت
            signal_strength = signals.get('overall_strength', 0.5)
            
            if signal_strength > 0.8:
                return 'فوري'
            elif signal_strength > 0.6:
                return 'خلال ساعة'
            else:
                return 'انتظار تحسن الإشارات'
                
        except Exception as e:
            return 'فوري'
    
    def find_optimal_exit(self, market_data, signals):
        """
        العثور على أفضل توقيت للخروج
        """
        try:
            return 'عند تحقيق الأهداف أو وقف الخسارة'
            
        except Exception as e:
            return 'حسب الأهداف'
    
    def calculate_market_timing_score(self, market_data):
        """
        حساب نقاط توقيت السوق
        """
        try:
            # تحليل بسيط لتوقيت السوق
            if 'Volume' in market_data:
                recent_volume = np.mean(market_data['Volume'][-5:])
                avg_volume = np.mean(market_data['Volume'])
                
                if recent_volume > avg_volume * 1.5:
                    return 0.8  # توقيت جيد
                elif recent_volume < avg_volume * 0.7:
                    return 0.3  # توقيت ضعيف
                else:
                    return 0.6  # توقيت متوسط
            
            return 0.5
            
        except Exception as e:
            return 0.5
    
    def analyze_intraday_patterns(self, market_data):
        """
        تحليل الأنماط اليومية
        """
        try:
            # تحليل مبسط للأنماط اليومية
            return {
                'best_entry_time': '10:00-11:00',
                'best_exit_time': '15:00-16:00',
                'avoid_times': ['09:30-10:00', '16:00-16:30']
            }
            
        except Exception as e:
            return {}
    
    def generate_timing_recommendations(self, timing):
        """
        إنتاج توصيات التوقيت
        """
        recommendations = []
        
        try:
            timing_score = timing.get('market_timing_score', 0.5)
            
            if timing_score > 0.7:
                recommendations.append("توقيت ممتاز للدخول")
            elif timing_score < 0.4:
                recommendations.append("فكر في تأجيل الدخول")
            
            return recommendations
            
        except Exception as e:
            return ["استخدم توقيت محافظ"]

# مثال على الاستخدام
if __name__ == "__main__":
    # إنشاء نظام إدارة المخاطر
    risk_assessor = RiskAssessment()
    position_sizer = PositionSizing()
    timing_optimizer = TimingOptimizer()
    
    print("⚡ نظام إدارة المخاطر المتقدم جاهز!")

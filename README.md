# 🤖 أداة التنبؤ الذكية للأسهم

أداة ذكاء اصطناعي احترافية للتنبؤ بأسعار الأسهم باستخدام تقنيات التعلم الآلي المتقدمة مع واجهة رسومية تفاعلية.

## ✨ المميزات

### 🧠 نماذج الذكاء الاصطناعي
- **LSTM (Long Short-Term Memory)**: للتنبؤ بالتسلسلات الزمنية
- **Random Forest**: للتحليل القائم على الأشجار
- **XGBoost**: للتعلم المعزز المتقدم
- **Ensemble Model**: نموذج مجمع يجمع تنبؤات جميع النماذج

### 📊 المؤشرات الفنية
- المتوسطات المتحركة (SMA, EMA)
- مؤشر القوة النسبية (RSI)
- MACD
- Bollinger Bands
- Average True Range (ATR)
- Stochastic Oscillator
- Williams %R

### 🎯 إمكانيات التنبؤ
- التنبؤ لفترات مختلفة (1، 3، 7، 14، 30 يوم)
- حساب مستوى الثقة في التنبؤات
- تحليل دقة التنبؤات السابقة
- تحليل معنويات السوق

### 🖥️ الواجهة الرسومية
- واجهة ويب تفاعلية باستخدام Streamlit
- رسوم بيانية تفاعلية مع Plotly
- عرض المؤشرات الفنية
- إحصائيات سريعة ومقاييس الأداء

## 🚀 التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
streamlit run app.py
```

سيفتح التطبيق في المتصفح على العنوان: `http://localhost:8501`

## 📁 هيكل المشروع

```
stock-predictor/
├── app.py                 # الواجهة الرسومية الرئيسية
├── predictor.py           # المتنبئ الرئيسي
├── models.py              # نماذج التعلم الآلي
├── data_collector.py      # جامع البيانات المالية
├── data_processor.py      # معالج البيانات
├── utils.py               # وظائف مساعدة
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المكتبات المطلوبة
├── README.md              # دليل الاستخدام
├── data/                  # مجلد البيانات
├── models/                # مجلد النماذج المحفوظة
├── logs/                  # مجلد السجلات
└── cache/                 # مجلد التخزين المؤقت
```

## 🎮 كيفية الاستخدام

### 1. اختيار السهم
- من الشريط الجانبي، اختر السهم المراد تحليله
- الأسهم المتاحة: AAPL, GOOGL, MSFT, AMZN, TSLA, META, NVDA, NFLX, BABA, AMD

### 2. تدريب النموذج
- اضغط على "تدريب نموذج جديد" لتدريب نماذج جديدة
- أو اضغط على "تحميل نموذج موجود" لاستخدام نماذج محفوظة مسبقاً

### 3. عرض التنبؤات
- بعد التدريب، ستظهر التنبؤات تلقائياً
- يمكنك اختيار فترة التنبؤ من الشريط الجانبي

### 4. تحليل النتائج
- راجع التنبؤات من النماذج المختلفة
- تحقق من مستوى الثقة
- ادرس الرسوم البيانية والمؤشرات الفنية

## 🔧 الإعدادات المتقدمة

### تخصيص النماذج
يمكنك تعديل إعدادات النماذج في ملف `config.py`:

```python
MODEL_CONFIG = {
    'lstm': {
        'sequence_length': 60,
        'epochs': 100,
        'batch_size': 32,
        'units': [50, 50, 50],
        'dropout': 0.2
    },
    'random_forest': {
        'n_estimators': 100,
        'max_depth': 10,
        'random_state': 42
    },
    'xgboost': {
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1,
        'random_state': 42
    }
}
```

### إضافة أسهم جديدة
أضف رموز الأسهم الجديدة في `config.py`:

```python
DATA_CONFIG = {
    'symbols': [
        'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA',
        'META', 'NVDA', 'NFLX', 'BABA', 'AMD',
        'YOUR_NEW_SYMBOL'  # أضف الرمز الجديد هنا
    ]
}
```

## 📊 مقاييس الأداء

التطبيق يحسب عدة مقاييس لتقييم أداء النماذج:

- **MSE (Mean Squared Error)**: متوسط مربع الخطأ
- **RMSE (Root Mean Squared Error)**: الجذر التربيعي لمتوسط مربع الخطأ
- **MAE (Mean Absolute Error)**: متوسط الخطأ المطلق
- **R² Score**: معامل التحديد
- **MAPE (Mean Absolute Percentage Error)**: متوسط النسبة المئوية للخطأ المطلق

## ⚠️ تنبيهات مهمة

1. **لا تعتمد على التنبؤات وحدها**: هذه الأداة للمساعدة في التحليل وليس للاستثمار المباشر
2. **الأسواق المالية متقلبة**: النتائج السابقة لا تضمن النتائج المستقبلية
3. **استشر خبير مالي**: قبل اتخاذ أي قرارات استثمارية
4. **تحديث البيانات**: تأكد من تحديث البيانات بانتظام للحصول على أفضل النتائج

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تثبيت TensorFlow**:
   ```bash
   pip install tensorflow==2.14.0
   ```

2. **خطأ في جلب البيانات**:
   - تحقق من اتصال الإنترنت
   - تأكد من صحة رمز السهم

3. **بطء في التدريب**:
   - قلل عدد epochs في إعدادات LSTM
   - استخدم GPU إذا كان متاحاً

4. **نفاد الذاكرة**:
   - قلل batch_size في إعدادات LSTM
   - قلل sequence_length

## 📈 تطوير مستقبلي

### ميزات مخططة
- [ ] دعم العملات المشفرة
- [ ] تحليل الأخبار والمشاعر
- [ ] تنبيهات تلقائية
- [ ] API للتكامل مع تطبيقات أخرى
- [ ] دعم المزيد من المؤشرات الفنية
- [ ] تحليل المحافظ الاستثمارية

### المساهمة
نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- مراجعة الوثائق
- التحقق من السجلات في مجلد `logs/`

---

**تذكير**: هذه أداة تعليمية وتحليلية. استخدمها بحكمة ولا تعتمد عليها وحدها في اتخاذ القرارات الاستثمارية.

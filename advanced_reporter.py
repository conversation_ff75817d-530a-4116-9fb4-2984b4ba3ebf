"""
مولد التقارير المتقدمة للتحليل المالي
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from advanced_config import REPORT_CONFIG, ADVANCED_PATHS, ADVANCED_MESSAGES

class AdvancedReporter:
    def __init__(self):
        self.report_config = REPORT_CONFIG
        self.reports_dir = ADVANCED_PATHS['reports']
        self.messages = ADVANCED_MESSAGES['ar']

    def generate_comprehensive_report(self, symbol, analysis_results, market_type='stocks'):
        """
        إنشاء تقرير شامل للرمز
        """
        try:
            print(self.messages['generating_report'])

            report = {
                'metadata': self.create_report_metadata(symbol, market_type),
                'executive_summary': self.create_executive_summary(analysis_results),
                'timeframe_analysis': self.create_timeframe_analysis(analysis_results),
                'technical_analysis': self.create_technical_analysis(analysis_results),
                'risk_assessment': self.create_risk_assessment(analysis_results),
                'trading_recommendations': self.create_trading_recommendations(analysis_results),
                'price_targets': self.calculate_price_targets(analysis_results),
                'market_outlook': self.create_market_outlook(analysis_results)
            }

            # حفظ التقرير
            self.save_report(symbol, report)

            return report

        except Exception as e:
            print(f"خطأ في إنشاء التقرير: {str(e)}")
            return {}

    def create_report_metadata(self, symbol, market_type):
        """
        إنشاء بيانات التقرير الأساسية
        """
        return {
            'symbol': symbol,
            'market_type': market_type,
            'report_date': datetime.now().isoformat(),
            'report_type': 'تحليل شامل متعدد الإطارات',
            'analyst': 'نظام الذكاء الاصطناعي المتقدم',
            'version': '2.0'
        }

    def create_executive_summary(self, analysis_results):
        """
        إنشاء الملخص التنفيذي
        """
        try:
            summary = {
                'overall_sentiment': 'محايد',
                'confidence_level': 0.5,
                'key_findings': [],
                'main_recommendation': 'مراقبة',
                'risk_level': 'متوسط'
            }

            # تحليل التوافق العام
            if 'multi_timeframe_confluence' in analysis_results:
                confluence = analysis_results['multi_timeframe_confluence']
                overall_trend = confluence.get('overall_trend', 'جانبي')
                trend_strength = confluence.get('trend_strength', 'ضعيف')

                if overall_trend == 'صاعد':
                    summary['overall_sentiment'] = 'إيجابي'
                    summary['main_recommendation'] = 'شراء' if trend_strength == 'قوي' else 'شراء محدود'
                    summary['confidence_level'] = 0.8 if trend_strength == 'قوي' else 0.6
                elif overall_trend == 'هابط':
                    summary['overall_sentiment'] = 'سلبي'
                    summary['main_recommendation'] = 'بيع' if trend_strength == 'قوي' else 'بيع محدود'
                    summary['confidence_level'] = 0.8 if trend_strength == 'قوي' else 0.6

                summary['key_findings'].append(f"الاتجاه العام: {overall_trend} ({trend_strength})")

            # تحليل المخاطر من الإطارات المختلفة
            risk_levels = []
            for timeframe, analysis in analysis_results.items():
                if timeframe != 'multi_timeframe_confluence' and 'risk_metrics' in analysis:
                    volatility = analysis['risk_metrics'].get('volatility', 0)
                    if volatility > 0.3:
                        risk_levels.append('عالي')
                    elif volatility > 0.2:
                        risk_levels.append('متوسط')
                    else:
                        risk_levels.append('منخفض')

            if risk_levels:
                if risk_levels.count('عالي') > len(risk_levels) / 2:
                    summary['risk_level'] = 'عالي'
                elif risk_levels.count('منخفض') > len(risk_levels) / 2:
                    summary['risk_level'] = 'منخفض'
                else:
                    summary['risk_level'] = 'متوسط'

            return summary

        except Exception as e:
            print(f"خطأ في إنشاء الملخص التنفيذي: {str(e)}")
            return {}

    def create_timeframe_analysis(self, analysis_results):
        """
        إنشاء تحليل الإطارات الزمنية
        """
        timeframe_analysis = {}

        timeframe_names = {
            '5min': '5 دقائق - التداول السريع',
            '1hour': 'ساعة واحدة - التداول اليومي',
            '4hour': '4 ساعات - التداول المتوسط',
            '1day': 'يوم واحد - الاستثمار طويل المدى'
        }

        for timeframe, analysis in analysis_results.items():
            if timeframe in timeframe_names:
                timeframe_analysis[timeframe] = {
                    'name': timeframe_names[timeframe],
                    'trend': self.extract_trend_info(analysis),
                    'momentum': self.extract_momentum_info(analysis),
                    'support_resistance': self.extract_sr_info(analysis),
                    'signals': self.extract_signals(analysis),
                    'recommendation': self.get_timeframe_recommendation(analysis)
                }

        return timeframe_analysis

    def extract_trend_info(self, analysis):
        """
        استخراج معلومات الاتجاه
        """
        trend_info = {}

        if 'trend_analysis' in analysis:
            trend = analysis['trend_analysis']
            trend_info['direction'] = trend.get('primary_trend', 'غير محدد')
            trend_info['strength'] = trend.get('trend_strength', 'غير محدد')
            trend_info['slope'] = trend.get('slope_direction', 'غير محدد')

        return trend_info

    def extract_momentum_info(self, analysis):
        """
        استخراج معلومات الزخم
        """
        momentum_info = {}

        if 'momentum_analysis' in analysis:
            momentum = analysis['momentum_analysis']
            momentum_info['rsi'] = {
                'signal': momentum.get('rsi_signal', 'غير محدد'),
                'action': momentum.get('rsi_action', 'غير محدد'),
                'value': momentum.get('rsi_value', 0)
            }
            momentum_info['macd'] = {
                'signal': momentum.get('macd_signal', 'غير محدد'),
                'action': momentum.get('macd_action', 'غير محدد')
            }
            momentum_info['stochastic'] = {
                'signal': momentum.get('stoch_signal', 'غير محدد')
            }

        return momentum_info

    def extract_sr_info(self, analysis):
        """
        استخراج معلومات الدعم والمقاومة
        """
        sr_info = {}

        if 'support_resistance' in analysis:
            sr = analysis['support_resistance']
            sr_info['pivot'] = sr.get('pivot', 0)
            sr_info['resistance_levels'] = [
                sr.get('resistance_1', 0),
                sr.get('resistance_2', 0)
            ]
            sr_info['support_levels'] = [
                sr.get('support_1', 0),
                sr.get('support_2', 0)
            ]
            sr_info['psychological_levels'] = sr.get('psychological_levels', [])

        return sr_info

    def extract_signals(self, analysis):
        """
        استخراج الإشارات
        """
        signals = []

        # إشارات الزخم
        if 'momentum_analysis' in analysis:
            momentum = analysis['momentum_analysis']
            if momentum.get('rsi_action') and momentum['rsi_action'] != 'مراقبة':
                signals.append({
                    'type': 'RSI',
                    'signal': momentum['rsi_action'],
                    'strength': 'متوسط'
                })

            if momentum.get('macd_action') and 'إشارة' in momentum['macd_action']:
                signals.append({
                    'type': 'MACD',
                    'signal': momentum['macd_action'],
                    'strength': 'قوي'
                })

        # إشارات الأنماط
        if 'pattern_analysis' in analysis:
            patterns = analysis['pattern_analysis']
            if 'breakouts' in patterns:
                for breakout_type, breakout_signal in patterns['breakouts'].items():
                    signals.append({
                        'type': 'اختراق',
                        'signal': breakout_signal,
                        'strength': 'قوي'
                    })

        return signals

    def get_timeframe_recommendation(self, analysis):
        """
        الحصول على توصية الإطار الزمني
        """
        recommendation = {
            'action': 'انتظار',
            'confidence': 0.5,
            'reasoning': []
        }

        # تحليل الاتجاه
        if 'trend_analysis' in analysis:
            trend = analysis['trend_analysis'].get('primary_trend', '')
            if 'صاعد قوي' in trend:
                recommendation['action'] = 'شراء'
                recommendation['confidence'] = 0.8
                recommendation['reasoning'].append('اتجاه صاعد قوي')
            elif 'هابط قوي' in trend:
                recommendation['action'] = 'بيع'
                recommendation['confidence'] = 0.8
                recommendation['reasoning'].append('اتجاه هابط قوي')

        # تحليل الزخم
        if 'momentum_analysis' in analysis:
            momentum = analysis['momentum_analysis']
            if 'إشارة شراء' in momentum.get('macd_action', ''):
                recommendation['confidence'] += 0.1
                recommendation['reasoning'].append('إشارة MACD إيجابية')
            elif 'إشارة بيع' in momentum.get('macd_action', ''):
                recommendation['confidence'] += 0.1
                recommendation['reasoning'].append('إشارة MACD سلبية')

        recommendation['confidence'] = min(recommendation['confidence'], 0.95)

        return recommendation

    def create_technical_analysis(self, analysis_results):
        """
        إنشاء التحليل الفني المفصل
        """
        technical_analysis = {
            'indicators_summary': {},
            'pattern_analysis': {},
            'volume_analysis': {},
            'volatility_analysis': {}
        }

        # تجميع المؤشرات من جميع الإطارات
        for timeframe, analysis in analysis_results.items():
            if timeframe != 'multi_timeframe_confluence':
                # تحليل المؤشرات
                if 'momentum_analysis' in analysis:
                    momentum = analysis['momentum_analysis']
                    technical_analysis['indicators_summary'][f'{timeframe}_RSI'] = {
                        'value': momentum.get('rsi_value', 0),
                        'signal': momentum.get('rsi_signal', 'غير محدد')
                    }

                # تحليل الأنماط
                if 'pattern_analysis' in analysis:
                    patterns = analysis['pattern_analysis']
                    technical_analysis['pattern_analysis'][timeframe] = patterns

                # تحليل الحجم
                if 'volume_analysis' in analysis:
                    volume = analysis['volume_analysis']
                    technical_analysis['volume_analysis'][timeframe] = volume

                # تحليل التقلبات
                if 'volatility_analysis' in analysis:
                    volatility = analysis['volatility_analysis']
                    technical_analysis['volatility_analysis'][timeframe] = volatility

        return technical_analysis

    def create_risk_assessment(self, analysis_results):
        """
        إنشاء تقييم المخاطر
        """
        risk_assessment = {
            'overall_risk': 'متوسط',
            'risk_factors': [],
            'risk_metrics': {},
            'position_sizing': {},
            'stop_loss_levels': {},
            'risk_reward_ratio': 0
        }

        # تجميع مقاييس المخاطر
        volatilities = []
        max_drawdowns = []

        for timeframe, analysis in analysis_results.items():
            if timeframe != 'multi_timeframe_confluence' and 'risk_metrics' in analysis:
                risk_metrics = analysis['risk_metrics']

                if 'volatility' in risk_metrics:
                    volatilities.append(risk_metrics['volatility'])

                if 'max_drawdown' in risk_metrics:
                    max_drawdowns.append(risk_metrics['max_drawdown'])

        # حساب المخاطر الإجمالية
        if volatilities:
            avg_volatility = np.mean(volatilities)
            risk_assessment['risk_metrics']['average_volatility'] = avg_volatility

            if avg_volatility > 0.4:
                risk_assessment['overall_risk'] = 'عالي'
                risk_assessment['risk_factors'].append('تقلبات عالية')
            elif avg_volatility < 0.15:
                risk_assessment['overall_risk'] = 'منخفض'

        if max_drawdowns:
            avg_drawdown = np.mean(max_drawdowns)
            risk_assessment['risk_metrics']['average_max_drawdown'] = avg_drawdown

            if avg_drawdown < -0.2:
                risk_assessment['risk_factors'].append('انخفاضات كبيرة محتملة')

        # توصيات حجم المركز
        if risk_assessment['overall_risk'] == 'عالي':
            risk_assessment['position_sizing']['recommended_size'] = '1-2% من المحفظة'
        elif risk_assessment['overall_risk'] == 'منخفض':
            risk_assessment['position_sizing']['recommended_size'] = '3-5% من المحفظة'
        else:
            risk_assessment['position_sizing']['recommended_size'] = '2-3% من المحفظة'

        return risk_assessment

    def create_trading_recommendations(self, analysis_results):
        """
        إنشاء التوصيات التداولية
        """
        recommendations = {
            'short_term': {},  # 5 دقائق - ساعة
            'medium_term': {}, # 4 ساعات
            'long_term': {}    # يوم
        }

        # توصيات قصيرة المدى
        if '5min' in analysis_results or '1hour' in analysis_results:
            short_term_analysis = analysis_results.get('5min', analysis_results.get('1hour', {}))
            recommendations['short_term'] = self.get_timeframe_recommendation(short_term_analysis)
            recommendations['short_term']['timeframe'] = 'قصير المدى (دقائق - ساعات)'
            recommendations['short_term']['suitable_for'] = 'المتداولين اليوميين'

        # توصيات متوسطة المدى
        if '4hour' in analysis_results:
            recommendations['medium_term'] = self.get_timeframe_recommendation(analysis_results['4hour'])
            recommendations['medium_term']['timeframe'] = 'متوسط المدى (ساعات - أيام)'
            recommendations['medium_term']['suitable_for'] = 'المتداولين المتأرجحين'

        # توصيات طويلة المدى
        if '1day' in analysis_results:
            recommendations['long_term'] = self.get_timeframe_recommendation(analysis_results['1day'])
            recommendations['long_term']['timeframe'] = 'طويل المدى (أيام - أسابيع)'
            recommendations['long_term']['suitable_for'] = 'المستثمرين'

        return recommendations

    def calculate_price_targets(self, analysis_results):
        """
        حساب الأهداف السعرية
        """
        price_targets = {
            'current_price': 0,
            'short_term_targets': {},
            'medium_term_targets': {},
            'long_term_targets': {},
            'stop_loss_levels': {}
        }

        # استخراج السعر الحالي من أي إطار زمني متاح
        for timeframe, analysis in analysis_results.items():
            if timeframe != 'multi_timeframe_confluence' and 'support_resistance' in analysis:
                sr = analysis['support_resistance']
                if 'pivot' in sr and sr['pivot'] > 0:
                    price_targets['current_price'] = sr['pivot']
                    break

        # حساب الأهداف بناءً على الدعم والمقاومة
        for timeframe, analysis in analysis_results.items():
            if timeframe != 'multi_timeframe_confluence' and 'support_resistance' in analysis:
                sr = analysis['support_resistance']

                targets = {
                    'resistance_1': sr.get('resistance_1', 0),
                    'resistance_2': sr.get('resistance_2', 0),
                    'support_1': sr.get('support_1', 0),
                    'support_2': sr.get('support_2', 0)
                }

                if timeframe in ['5min', '1hour']:
                    price_targets['short_term_targets'][timeframe] = targets
                elif timeframe == '4hour':
                    price_targets['medium_term_targets'][timeframe] = targets
                elif timeframe == '1day':
                    price_targets['long_term_targets'][timeframe] = targets

        return price_targets

    def create_market_outlook(self, analysis_results):
        """
        إنشاء نظرة السوق
        """
        outlook = {
            'overall_sentiment': 'محايد',
            'key_drivers': [],
            'potential_catalysts': [],
            'risks_to_watch': [],
            'market_regime': 'طبيعي'
        }

        # تحليل النظرة العامة
        if 'multi_timeframe_confluence' in analysis_results:
            confluence = analysis_results['multi_timeframe_confluence']
            overall_trend = confluence.get('overall_trend', 'جانبي')

            if overall_trend == 'صاعد':
                outlook['overall_sentiment'] = 'إيجابي'
                outlook['key_drivers'].append('اتجاه صاعد متسق عبر الإطارات الزمنية')
            elif overall_trend == 'هابط':
                outlook['overall_sentiment'] = 'سلبي'
                outlook['key_drivers'].append('ضغط بيعي عبر الإطارات الزمنية')

        # تحليل التقلبات
        high_volatility_count = 0
        for timeframe, analysis in analysis_results.items():
            if timeframe != 'multi_timeframe_confluence' and 'volatility_analysis' in analysis:
                volatility = analysis['volatility_analysis']
                if volatility.get('volatility_level') == 'عالي':
                    high_volatility_count += 1

        if high_volatility_count > 2:
            outlook['market_regime'] = 'تقلبات عالية'
            outlook['risks_to_watch'].append('تقلبات سعرية حادة')

        return outlook

    def save_report(self, symbol, report):
        """
        حفظ التقرير
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{symbol}_comprehensive_report_{timestamp}.json"
            filepath = os.path.join(self.reports_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            print(f"تم حفظ التقرير: {filepath}")

        except Exception as e:
            print(f"خطأ في حفظ التقرير: {str(e)}")

    def generate_html_report(self, symbol, report):
        """
        إنشاء تقرير HTML
        """
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير تحليل {symbol}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ background: #1f77b4; color: white; padding: 20px; text-align: center; }}
                    .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
                    .recommendation {{ background: #f0f8ff; padding: 10px; border-left: 4px solid #1f77b4; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>تقرير تحليل شامل - {symbol}</h1>
                    <p>تاريخ التقرير: {report['metadata']['report_date']}</p>
                </div>

                <div class="section">
                    <h2>الملخص التنفيذي</h2>
                    <div class="recommendation">
                        <p><strong>التوصية الرئيسية:</strong> {report['executive_summary']['main_recommendation']}</p>
                        <p><strong>مستوى الثقة:</strong> {report['executive_summary']['confidence_level']:.1%}</p>
                        <p><strong>مستوى المخاطر:</strong> {report['executive_summary']['risk_level']}</p>
                    </div>
                </div>

                <div class="section">
                    <h2>تحليل الإطارات الزمنية</h2>
                    {self._generate_timeframe_html(report.get('timeframe_analysis', {}))}
                </div>

                <div class="section">
                    <h2>تقييم المخاطر</h2>
                    <p><strong>المخاطر الإجمالية:</strong> {report['risk_assessment']['overall_risk']}</p>
                    <p><strong>حجم المركز المقترح:</strong> {report['risk_assessment']['position_sizing'].get('recommended_size', 'غير محدد')}</p>
                </div>
            </body>
            </html>
            """

            # حفظ ملف HTML
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{symbol}_report_{timestamp}.html"
            filepath = os.path.join(self.reports_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"تم حفظ التقرير HTML: {filepath}")
            return filepath

        except Exception as e:
            print(f"خطأ في إنشاء تقرير HTML: {str(e)}")
            return None

    def _generate_timeframe_html(self, timeframe_analysis):
        """
        إنشاء HTML لتحليل الإطارات الزمنية
        """
        html = ""
        for timeframe, analysis in timeframe_analysis.items():
            html += f"""
            <div style="margin: 10px 0; padding: 10px; background: #f9f9f9;">
                <h3>{analysis.get('name', timeframe)}</h3>
                <p><strong>الاتجاه:</strong> {analysis.get('trend', {}).get('direction', 'غير محدد')}</p>
                <p><strong>التوصية:</strong> {analysis.get('recommendation', {}).get('action', 'انتظار')}</p>
            </div>
            """
        return html

# مثال على الاستخدام
if __name__ == "__main__":
    # مثال على بيانات تحليل وهمية
    sample_analysis = {
        '5min': {
            'trend_analysis': {'primary_trend': 'صاعد', 'trend_strength': 'متوسط'},
            'momentum_analysis': {'rsi_signal': 'متوازن', 'rsi_value': 55},
            'support_resistance': {'pivot': 150.0, 'resistance_1': 155.0, 'support_1': 145.0}
        }
    }

    reporter = AdvancedReporter()
    report = reporter.generate_comprehensive_report('AAPL', sample_analysis)

    print("✅ تم إنشاء التقرير بنجاح!")

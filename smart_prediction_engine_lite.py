"""
محرك التنبؤ الذكي المبسط (بدون TensorFlow)
نظام تنبؤ بأسعار الأسهم مع تعلم آلي متقدم
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import ta
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class SmartPredictionEngineLite:
    """
    محرك التنبؤ الذكي المبسط
    """
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.prediction_horizons = ['5min', '10min', '1hour', '4hour', '1day']
        self.model_performance = {}
        
    def prepare_features(self, data):
        """
        إعداد المؤشرات الفنية المتقدمة للتنبؤ
        """
        try:
            df = data.copy()
            
            # المؤشرات الأساسية
            df['sma_5'] = ta.trend.sma_indicator(df['Close'], window=5)
            df['sma_10'] = ta.trend.sma_indicator(df['Close'], window=10)
            df['sma_20'] = ta.trend.sma_indicator(df['Close'], window=20)
            df['sma_50'] = ta.trend.sma_indicator(df['Close'], window=50)
            
            df['ema_5'] = ta.trend.ema_indicator(df['Close'], window=5)
            df['ema_10'] = ta.trend.ema_indicator(df['Close'], window=10)
            df['ema_20'] = ta.trend.ema_indicator(df['Close'], window=20)
            
            # مؤشرات الزخم
            df['rsi'] = ta.momentum.rsi(df['Close'], window=14)
            df['rsi_fast'] = ta.momentum.rsi(df['Close'], window=7)
            df['rsi_slow'] = ta.momentum.rsi(df['Close'], window=21)
            
            df['stoch_k'] = ta.momentum.stoch(df['High'], df['Low'], df['Close'])
            df['stoch_d'] = ta.momentum.stoch_signal(df['High'], df['Low'], df['Close'])
            
            df['williams_r'] = ta.momentum.williams_r(df['High'], df['Low'], df['Close'])
            df['cci'] = ta.trend.cci(df['High'], df['Low'], df['Close'])
            
            # MACD
            df['macd'] = ta.trend.macd(df['Close'])
            df['macd_signal'] = ta.trend.macd_signal(df['Close'])
            df['macd_diff'] = ta.trend.macd_diff(df['Close'])
            
            # Bollinger Bands
            df['bb_high'] = ta.volatility.bollinger_hband(df['Close'])
            df['bb_low'] = ta.volatility.bollinger_lband(df['Close'])
            df['bb_mid'] = ta.volatility.bollinger_mavg(df['Close'])
            df['bb_width'] = ta.volatility.bollinger_wband(df['Close'])
            df['bb_percent'] = ta.volatility.bollinger_pband(df['Close'])
            
            # مؤشرات التقلبات
            df['atr'] = ta.volatility.average_true_range(df['High'], df['Low'], df['Close'])
            df['kc_high'] = ta.volatility.keltner_channel_hband(df['High'], df['Low'], df['Close'])
            df['kc_low'] = ta.volatility.keltner_channel_lband(df['High'], df['Low'], df['Close'])
            
            # مؤشرات الحجم
            df['obv'] = ta.volume.on_balance_volume(df['Close'], df['Volume'])
            df['ad'] = ta.volume.acc_dist_index(df['High'], df['Low'], df['Close'], df['Volume'])
            df['cmf'] = ta.volume.chaikin_money_flow(df['High'], df['Low'], df['Close'], df['Volume'])
            
            # مؤشرات الاتجاه
            df['adx'] = ta.trend.adx(df['High'], df['Low'], df['Close'])
            df['psar'] = ta.trend.psar_down(df['High'], df['Low'], df['Close'])
            
            # مؤشرات مخصصة
            df['price_change'] = df['Close'].pct_change()
            df['volume_change'] = df['Volume'].pct_change()
            df['high_low_ratio'] = (df['High'] - df['Low']) / df['Close']
            df['close_open_ratio'] = (df['Close'] - df['Open']) / df['Open']
            
            # مؤشرات الزمن
            df['hour'] = df.index.hour if hasattr(df.index, 'hour') else 12
            df['day_of_week'] = df.index.dayofweek if hasattr(df.index, 'dayofweek') else 1
            df['month'] = df.index.month if hasattr(df.index, 'month') else 1
            
            # إزالة القيم المفقودة
            df = df.dropna()
            
            # تحديد أعمدة المؤشرات
            feature_cols = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume']]
            self.feature_columns = feature_cols
            
            return df[feature_cols + ['Close']]
            
        except Exception as e:
            print(f"خطأ في إعداد المؤشرات: {str(e)}")
            return data
    
    def create_sequences(self, data, sequence_length=30, prediction_steps=1):
        """
        إنشاء تسلسلات زمنية للتدريب (مبسط للنماذج التقليدية)
        """
        try:
            features = data[self.feature_columns].values
            targets = data['Close'].values
            
            X, y = [], []
            
            for i in range(sequence_length, len(data) - prediction_steps + 1):
                # أخذ آخر قيم من التسلسل (مبسط)
                X.append(features[i-1])  # آخر نقطة فقط
                y.append(targets[i + prediction_steps - 1])
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            print(f"خطأ في إنشاء التسلسلات: {str(e)}")
            return np.array([]), np.array([])
    
    def train_ensemble_models(self, data, horizon, test_size=0.2):
        """
        تدريب مجموعة من النماذج المتقدمة (بدون Deep Learning)
        """
        try:
            print(f"🤖 تدريب النماذج للأفق الزمني: {horizon}")
            
            # إعداد البيانات
            processed_data = self.prepare_features(data)
            
            if len(processed_data) < 100:
                print("⚠️ البيانات غير كافية للتدريب")
                return False
            
            # تحديد خطوات التنبؤ
            prediction_steps = {
                '5min': 1,
                '10min': 2,
                '1hour': 12,
                '4hour': 48,
                '1day': 288
            }
            
            steps = prediction_steps.get(horizon, 1)
            
            # إنشاء التسلسلات
            X, y = self.create_sequences(processed_data, sequence_length=30, prediction_steps=steps)
            
            if len(X) == 0:
                print("⚠️ فشل في إنشاء التسلسلات")
                return False
            
            # تقسيم البيانات
            split_idx = int(len(X) * (1 - test_size))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # تطبيع البيانات
            scaler_X = StandardScaler()
            scaler_y = MinMaxScaler()
            
            X_train_scaled = scaler_X.fit_transform(X_train)
            X_test_scaled = scaler_X.transform(X_test)
            
            y_train_scaled = scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
            y_test_scaled = scaler_y.transform(y_test.reshape(-1, 1)).flatten()
            
            # حفظ المقاييس
            self.scalers[f'{horizon}_X'] = scaler_X
            self.scalers[f'{horizon}_y'] = scaler_y
            
            # تدريب النماذج
            models = {}
            
            # 1. Random Forest
            print("   🌲 تدريب Random Forest...")
            rf_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            rf_model.fit(X_train_scaled, y_train_scaled)
            models['random_forest'] = rf_model
            
            # 2. Gradient Boosting
            print("   🚀 تدريب Gradient Boosting...")
            gb_model = GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
            gb_model.fit(X_train_scaled, y_train_scaled)
            models['gradient_boosting'] = gb_model
            
            # 3. Support Vector Regression
            print("   🎯 تدريب SVR...")
            svr_model = SVR(
                kernel='rbf',
                C=100,
                gamma='scale'
            )
            svr_model.fit(X_train_scaled, y_train_scaled)
            models['svr'] = svr_model
            
            # 4. Linear Regression (للمقارنة)
            print("   📈 تدريب Linear Regression...")
            lr_model = LinearRegression()
            lr_model.fit(X_train_scaled, y_train_scaled)
            models['linear_regression'] = lr_model
            
            # تقييم النماذج
            print("   📈 تقييم النماذج...")
            performance = {}
            
            for name, model in models.items():
                try:
                    y_pred_scaled = model.predict(X_test_scaled)
                    
                    # إعادة تحويل التنبؤات
                    y_pred = scaler_y.inverse_transform(y_pred_scaled.reshape(-1, 1)).flatten()
                    y_true = scaler_y.inverse_transform(y_test_scaled.reshape(-1, 1)).flatten()
                    
                    # حساب المقاييس
                    mae = mean_absolute_error(y_true, y_pred)
                    mse = mean_squared_error(y_true, y_pred)
                    rmse = np.sqrt(mse)
                    r2 = r2_score(y_true, y_pred)
                    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
                    
                    performance[name] = {
                        'mae': mae,
                        'mse': mse,
                        'rmse': rmse,
                        'r2': r2,
                        'mape': mape,
                        'accuracy': max(0, (100 - mape))
                    }
                    
                    print(f"     {name}: دقة {performance[name]['accuracy']:.1f}%")
                    
                except Exception as e:
                    print(f"     خطأ في تقييم {name}: {str(e)}")
            
            # حفظ النماذج والأداء
            self.models[horizon] = models
            self.model_performance[horizon] = performance
            
            # حفظ النماذج على القرص
            self.save_models(horizon)
            
            print(f"✅ تم تدريب النماذج بنجاح للأفق {horizon}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تدريب النماذج: {str(e)}")
            return False
    
    def predict_price(self, data, horizon, method='ensemble'):
        """
        التنبؤ بالسعر للأفق الزمني المحدد
        """
        try:
            if horizon not in self.models:
                print(f"⚠️ لم يتم تدريب نماذج للأفق {horizon}")
                return None
            
            # إعداد البيانات
            processed_data = self.prepare_features(data)
            
            if len(processed_data) < 30:
                print("⚠️ البيانات غير كافية للتنبؤ")
                return None
            
            # أخذ آخر نقطة
            last_features = processed_data[self.feature_columns].tail(1).values
            
            # تطبيع البيانات
            scaler_X = self.scalers.get(f'{horizon}_X')
            scaler_y = self.scalers.get(f'{horizon}_y')
            
            if not scaler_X or not scaler_y:
                print(f"⚠️ لم يتم العثور على المقاييس للأفق {horizon}")
                return None
            
            # تحضير البيانات للتنبؤ
            X_pred_scaled = scaler_X.transform(last_features)
            
            # التنبؤ بالنماذج المختلفة
            predictions = {}
            models = self.models[horizon]
            
            for name, model in models.items():
                try:
                    pred_scaled = model.predict(X_pred_scaled)[0]
                    
                    # إعادة تحويل التنبؤ
                    pred = scaler_y.inverse_transform([[pred_scaled]])[0][0]
                    predictions[name] = pred
                    
                except Exception as e:
                    print(f"خطأ في التنبؤ بـ {name}: {str(e)}")
            
            if not predictions:
                return None
            
            # حساب التنبؤ النهائي
            if method == 'ensemble':
                # متوسط مرجح بناءً على الأداء
                weights = {}
                total_accuracy = 0
                
                for name in predictions.keys():
                    if name in self.model_performance.get(horizon, {}):
                        accuracy = self.model_performance[horizon][name].get('accuracy', 50)
                        weights[name] = max(accuracy, 10)  # حد أدنى 10%
                        total_accuracy += weights[name]
                
                if total_accuracy > 0:
                    final_prediction = sum(pred * weights.get(name, 25) for name, pred in predictions.items()) / total_accuracy
                else:
                    final_prediction = np.mean(list(predictions.values()))
            else:
                # استخدام نموذج محدد
                final_prediction = predictions.get(method, np.mean(list(predictions.values())))
            
            current_price = data['Close'].iloc[-1]
            change_percent = ((final_prediction - current_price) / current_price) * 100
            
            return {
                'predicted_price': final_prediction,
                'current_price': current_price,
                'change_amount': final_prediction - current_price,
                'change_percent': change_percent,
                'horizon': horizon,
                'individual_predictions': predictions,
                'confidence': self.calculate_prediction_confidence(predictions, horizon),
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            print(f"❌ خطأ في التنبؤ: {str(e)}")
            return None
    
    def calculate_prediction_confidence(self, predictions, horizon):
        """
        حساب مستوى الثقة في التنبؤ
        """
        try:
            if not predictions or len(predictions) < 2:
                return 0.5
            
            # حساب التباين بين التنبؤات
            pred_values = list(predictions.values())
            std_dev = np.std(pred_values)
            mean_pred = np.mean(pred_values)
            
            # حساب معامل التباين
            cv = std_dev / mean_pred if mean_pred != 0 else 1
            
            # تحويل إلى مستوى ثقة (كلما قل التباين زادت الثقة)
            confidence = max(0.1, min(0.95, 1 - cv))
            
            # تعديل بناءً على أداء النماذج
            if horizon in self.model_performance:
                avg_accuracy = np.mean([perf.get('accuracy', 50) for perf in self.model_performance[horizon].values()])
                confidence = (confidence + avg_accuracy/100) / 2
            
            return confidence
            
        except Exception as e:
            return 0.5
    
    def save_models(self, horizon):
        """
        حفظ النماذج المدربة
        """
        try:
            import os
            if not os.path.exists('models'):
                os.makedirs('models')
            
            models = self.models.get(horizon, {})
            
            for name, model in models.items():
                joblib.dump(model, f'models/{horizon}_{name}_model.pkl')
            
            # حفظ المقاييس
            if f'{horizon}_X' in self.scalers:
                joblib.dump(self.scalers[f'{horizon}_X'], f'models/{horizon}_scaler_X.pkl')
            if f'{horizon}_y' in self.scalers:
                joblib.dump(self.scalers[f'{horizon}_y'], f'models/{horizon}_scaler_y.pkl')
            
            print(f"💾 تم حفظ نماذج {horizon}")
            
        except Exception as e:
            print(f"خطأ في حفظ النماذج: {str(e)}")
    
    def load_models(self, horizon):
        """
        تحميل النماذج المحفوظة
        """
        try:
            import os
            if not os.path.exists('models'):
                os.makedirs('models')
                return False
            
            models = {}
            
            # تحميل النماذج
            model_files = {
                'random_forest': f'models/{horizon}_random_forest_model.pkl',
                'gradient_boosting': f'models/{horizon}_gradient_boosting_model.pkl',
                'svr': f'models/{horizon}_svr_model.pkl',
                'linear_regression': f'models/{horizon}_linear_regression_model.pkl'
            }
            
            for name, file_path in model_files.items():
                if os.path.exists(file_path):
                    models[name] = joblib.load(file_path)
            
            if models:
                self.models[horizon] = models
                
                # تحميل المقاييس
                scaler_X_path = f'models/{horizon}_scaler_X.pkl'
                scaler_y_path = f'models/{horizon}_scaler_y.pkl'
                
                if os.path.exists(scaler_X_path):
                    self.scalers[f'{horizon}_X'] = joblib.load(scaler_X_path)
                if os.path.exists(scaler_y_path):
                    self.scalers[f'{horizon}_y'] = joblib.load(scaler_y_path)
                
                print(f"📂 تم تحميل نماذج {horizon}")
                return True
            
            return False
            
        except Exception as e:
            print(f"خطأ في تحميل النماذج: {str(e)}")
            return False

# مثال على الاستخدام
if __name__ == "__main__":
    print("🤖 محرك التنبؤ الذكي المبسط جاهز!")

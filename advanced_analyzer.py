"""
محلل السوق المتقدم مع الذكاء الاصطناعي
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import ta
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

from advanced_config import ANALYSIS_CONFIG, REPORT_CONFIG, RISK_MANAGEMENT_CONFIG

class AdvancedMarketAnalyzer:
    def __init__(self):
        self.analysis_config = ANALYSIS_CONFIG
        self.report_config = REPORT_CONFIG
        self.scaler = StandardScaler()
        
    def analyze_multi_timeframe(self, data_dict, symbol):
        """
        تحليل متعدد الإطارات الزمنية
        """
        analysis_results = {}
        
        try:
            for timeframe, data in data_dict.items():
                if data is not None and not data.empty:
                    print(f"تحليل {timeframe} لـ {symbol}...")
                    
                    analysis = {
                        'trend_analysis': self.analyze_trend(data),
                        'momentum_analysis': self.analyze_momentum(data),
                        'volatility_analysis': self.analyze_volatility(data),
                        'volume_analysis': self.analyze_volume(data),
                        'support_resistance': self.find_support_resistance(data),
                        'pattern_analysis': self.analyze_patterns(data),
                        'market_structure': self.analyze_market_structure(data),
                        'risk_metrics': self.calculate_risk_metrics(data)
                    }
                    
                    analysis_results[timeframe] = analysis
            
            # تحليل التوافق بين الإطارات الزمنية
            analysis_results['multi_timeframe_confluence'] = self.analyze_timeframe_confluence(analysis_results)
            
            return analysis_results
            
        except Exception as e:
            print(f"خطأ في التحليل متعدد الإطارات: {str(e)}")
            return {}
    
    def analyze_trend(self, data):
        """
        تحليل الاتجاه المتقدم
        """
        try:
            trend_analysis = {}
            
            # اتجاه المتوسطات المتحركة
            sma_20 = data['SMA_20'].iloc[-1] if 'SMA_20' in data.columns else None
            sma_50 = data['SMA_50'].iloc[-1] if 'SMA_50' in data.columns else None
            sma_200 = data['SMA_200'].iloc[-1] if 'SMA_200' in data.columns else None
            current_price = data['Close'].iloc[-1]
            
            # تحديد الاتجاه الرئيسي
            if sma_20 and sma_50 and sma_200:
                if current_price > sma_20 > sma_50 > sma_200:
                    trend_analysis['primary_trend'] = 'صاعد قوي'
                    trend_analysis['trend_strength'] = 'قوي'
                elif current_price > sma_20 > sma_50:
                    trend_analysis['primary_trend'] = 'صاعد'
                    trend_analysis['trend_strength'] = 'متوسط'
                elif current_price < sma_20 < sma_50 < sma_200:
                    trend_analysis['primary_trend'] = 'هابط قوي'
                    trend_analysis['trend_strength'] = 'قوي'
                elif current_price < sma_20 < sma_50:
                    trend_analysis['primary_trend'] = 'هابط'
                    trend_analysis['trend_strength'] = 'متوسط'
                else:
                    trend_analysis['primary_trend'] = 'جانبي'
                    trend_analysis['trend_strength'] = 'ضعيف'
            
            # تحليل ADX للقوة
            if 'ADX' in data.columns:
                adx_value = data['ADX'].iloc[-1]
                if adx_value > 25:
                    trend_analysis['trend_strength_adx'] = 'قوي'
                elif adx_value > 20:
                    trend_analysis['trend_strength_adx'] = 'متوسط'
                else:
                    trend_analysis['trend_strength_adx'] = 'ضعيف'
            
            # تحليل انحدار الاتجاه
            prices = data['Close'].tail(20).values
            x = np.arange(len(prices))
            slope = np.polyfit(x, prices, 1)[0]
            trend_analysis['slope'] = slope
            trend_analysis['slope_direction'] = 'صاعد' if slope > 0 else 'هابط'
            
            return trend_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل الاتجاه: {str(e)}")
            return {}
    
    def analyze_momentum(self, data):
        """
        تحليل الزخم المتقدم
        """
        try:
            momentum_analysis = {}
            
            # تحليل RSI
            if 'RSI' in data.columns:
                rsi_value = data['RSI'].iloc[-1]
                if rsi_value > 70:
                    momentum_analysis['rsi_signal'] = 'تشبع شرائي'
                    momentum_analysis['rsi_action'] = 'احتمال تصحيح'
                elif rsi_value < 30:
                    momentum_analysis['rsi_signal'] = 'تشبع بيعي'
                    momentum_analysis['rsi_action'] = 'احتمال ارتداد'
                else:
                    momentum_analysis['rsi_signal'] = 'متوازن'
                    momentum_analysis['rsi_action'] = 'مراقبة'
                
                momentum_analysis['rsi_value'] = rsi_value
            
            # تحليل MACD
            if all(col in data.columns for col in ['MACD', 'MACD_signal']):
                macd = data['MACD'].iloc[-1]
                macd_signal = data['MACD_signal'].iloc[-1]
                macd_prev = data['MACD'].iloc[-2]
                macd_signal_prev = data['MACD_signal'].iloc[-2]
                
                # إشارة التقاطع
                if macd > macd_signal and macd_prev <= macd_signal_prev:
                    momentum_analysis['macd_signal'] = 'تقاطع صاعد'
                    momentum_analysis['macd_action'] = 'إشارة شراء'
                elif macd < macd_signal and macd_prev >= macd_signal_prev:
                    momentum_analysis['macd_signal'] = 'تقاطع هابط'
                    momentum_analysis['macd_action'] = 'إشارة بيع'
                else:
                    momentum_analysis['macd_signal'] = 'لا توجد إشارة'
                    momentum_analysis['macd_action'] = 'انتظار'
            
            # تحليل Stochastic
            if all(col in data.columns for col in ['Stoch_K', 'Stoch_D']):
                stoch_k = data['Stoch_K'].iloc[-1]
                stoch_d = data['Stoch_D'].iloc[-1]
                
                if stoch_k > 80 and stoch_d > 80:
                    momentum_analysis['stoch_signal'] = 'تشبع شرائي'
                elif stoch_k < 20 and stoch_d < 20:
                    momentum_analysis['stoch_signal'] = 'تشبع بيعي'
                else:
                    momentum_analysis['stoch_signal'] = 'متوازن'
            
            return momentum_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل الزخم: {str(e)}")
            return {}
    
    def analyze_volatility(self, data):
        """
        تحليل التقلبات
        """
        try:
            volatility_analysis = {}
            
            # تحليل ATR
            if 'ATR' in data.columns:
                atr_current = data['ATR'].iloc[-1]
                atr_avg = data['ATR'].tail(20).mean()
                
                volatility_analysis['atr_current'] = atr_current
                volatility_analysis['atr_average'] = atr_avg
                
                if atr_current > atr_avg * 1.5:
                    volatility_analysis['volatility_level'] = 'عالي'
                elif atr_current < atr_avg * 0.7:
                    volatility_analysis['volatility_level'] = 'منخفض'
                else:
                    volatility_analysis['volatility_level'] = 'طبيعي'
            
            # تحليل Bollinger Bands
            if all(col in data.columns for col in ['BB_upper', 'BB_lower', 'BB_width']):
                bb_width = data['BB_width'].iloc[-1]
                bb_width_avg = data['BB_width'].tail(20).mean()
                current_price = data['Close'].iloc[-1]
                bb_upper = data['BB_upper'].iloc[-1]
                bb_lower = data['BB_lower'].iloc[-1]
                
                # موقع السعر في البولينجر
                bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
                volatility_analysis['bb_position'] = bb_position
                
                if bb_position > 0.8:
                    volatility_analysis['bb_signal'] = 'قرب الحد العلوي'
                elif bb_position < 0.2:
                    volatility_analysis['bb_signal'] = 'قرب الحد السفلي'
                else:
                    volatility_analysis['bb_signal'] = 'وسط النطاق'
                
                # عرض البولينجر
                if bb_width > bb_width_avg * 1.2:
                    volatility_analysis['bb_width_signal'] = 'توسع - زيادة تقلبات'
                elif bb_width < bb_width_avg * 0.8:
                    volatility_analysis['bb_width_signal'] = 'انقباض - انتظار كسر'
                else:
                    volatility_analysis['bb_width_signal'] = 'طبيعي'
            
            return volatility_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل التقلبات: {str(e)}")
            return {}
    
    def analyze_volume(self, data):
        """
        تحليل الحجم المتقدم
        """
        try:
            volume_analysis = {}
            
            if 'Volume' in data.columns:
                current_volume = data['Volume'].iloc[-1]
                avg_volume = data['Volume'].tail(20).mean()
                
                volume_analysis['current_volume'] = current_volume
                volume_analysis['average_volume'] = avg_volume
                volume_analysis['volume_ratio'] = current_volume / avg_volume
                
                # تحليل قوة الحجم
                if current_volume > avg_volume * 2:
                    volume_analysis['volume_strength'] = 'قوي جداً'
                elif current_volume > avg_volume * 1.5:
                    volume_analysis['volume_strength'] = 'قوي'
                elif current_volume > avg_volume:
                    volume_analysis['volume_strength'] = 'فوق المتوسط'
                else:
                    volume_analysis['volume_strength'] = 'ضعيف'
            
            # تحليل OBV
            if 'OBV' in data.columns:
                obv_trend = data['OBV'].tail(10).diff().mean()
                if obv_trend > 0:
                    volume_analysis['obv_trend'] = 'صاعد'
                elif obv_trend < 0:
                    volume_analysis['obv_trend'] = 'هابط'
                else:
                    volume_analysis['obv_trend'] = 'جانبي'
            
            return volume_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل الحجم: {str(e)}")
            return {}
    
    def find_support_resistance(self, data):
        """
        العثور على مستويات الدعم والمقاومة
        """
        try:
            sr_analysis = {}
            
            # استخدام نقاط البيفوت
            if 'Pivot' in data.columns:
                sr_analysis['pivot'] = data['Pivot'].iloc[-1]
                sr_analysis['resistance_1'] = data['R1'].iloc[-1]
                sr_analysis['resistance_2'] = data['R2'].iloc[-1]
                sr_analysis['support_1'] = data['S1'].iloc[-1]
                sr_analysis['support_2'] = data['S2'].iloc[-1]
            
            # العثور على مستويات نفسية
            current_price = data['Close'].iloc[-1]
            round_numbers = []
            
            # أرقام مدورة قريبة
            for i in range(-5, 6):
                round_num = round(current_price + i, -1)  # تدوير لأقرب 10
                if abs(round_num - current_price) / current_price < 0.1:  # ضمن 10%
                    round_numbers.append(round_num)
            
            sr_analysis['psychological_levels'] = round_numbers
            
            return sr_analysis
            
        except Exception as e:
            print(f"خطأ في العثور على الدعم والمقاومة: {str(e)}")
            return {}
    
    def analyze_patterns(self, data):
        """
        تحليل الأنماط المتقدم
        """
        try:
            pattern_analysis = {}
            
            # تحليل أنماط الشموع
            pattern_analysis['candlestick'] = self.detect_candlestick_patterns(data)
            
            # تحليل الاختراقات
            pattern_analysis['breakouts'] = self.detect_breakouts(data)
            
            # تحليل التباعد
            pattern_analysis['divergences'] = self.detect_divergences(data)
            
            return pattern_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل الأنماط: {str(e)}")
            return {}
    
    def detect_candlestick_patterns(self, data):
        """
        اكتشاف أنماط الشموع اليابانية
        """
        patterns = {}
        
        try:
            # الحصول على آخر شمعة
            last_candle = data.iloc[-1]
            prev_candle = data.iloc[-2] if len(data) > 1 else None
            
            open_price = last_candle['Open']
            close_price = last_candle['Close']
            high_price = last_candle['High']
            low_price = last_candle['Low']
            
            body_size = abs(close_price - open_price)
            total_range = high_price - low_price
            upper_shadow = high_price - max(open_price, close_price)
            lower_shadow = min(open_price, close_price) - low_price
            
            # Doji
            if body_size / total_range < 0.1:
                patterns['doji'] = True
                patterns['doji_signal'] = 'تردد في السوق'
            
            # Hammer
            if (lower_shadow > 2 * body_size and upper_shadow < body_size and 
                close_price < open_price):
                patterns['hammer'] = True
                patterns['hammer_signal'] = 'احتمال ارتداد صاعد'
            
            # Shooting Star
            if (upper_shadow > 2 * body_size and lower_shadow < body_size and 
                close_price < open_price):
                patterns['shooting_star'] = True
                patterns['shooting_star_signal'] = 'احتمال ارتداد هابط'
            
            # Engulfing Pattern
            if prev_candle is not None:
                prev_body = abs(prev_candle['Close'] - prev_candle['Open'])
                if (body_size > prev_body * 1.5 and
                    ((close_price > open_price and prev_candle['Close'] < prev_candle['Open']) or
                     (close_price < open_price and prev_candle['Close'] > prev_candle['Open']))):
                    patterns['engulfing'] = True
                    if close_price > open_price:
                        patterns['engulfing_signal'] = 'نمط ابتلاع صاعد'
                    else:
                        patterns['engulfing_signal'] = 'نمط ابتلاع هابط'
            
            return patterns
            
        except Exception as e:
            print(f"خطأ في اكتشاف أنماط الشموع: {str(e)}")
            return {}
    
    def detect_breakouts(self, data):
        """
        اكتشاف الاختراقات
        """
        breakouts = {}
        
        try:
            current_price = data['Close'].iloc[-1]
            
            # اختراق المتوسطات المتحركة
            if 'SMA_20' in data.columns:
                sma_20 = data['SMA_20'].iloc[-1]
                prev_price = data['Close'].iloc[-2]
                prev_sma_20 = data['SMA_20'].iloc[-2]
                
                if current_price > sma_20 and prev_price <= prev_sma_20:
                    breakouts['sma_20_breakout'] = 'اختراق صاعد للمتوسط 20'
                elif current_price < sma_20 and prev_price >= prev_sma_20:
                    breakouts['sma_20_breakout'] = 'اختراق هابط للمتوسط 20'
            
            # اختراق البولينجر
            if all(col in data.columns for col in ['BB_upper', 'BB_lower']):
                bb_upper = data['BB_upper'].iloc[-1]
                bb_lower = data['BB_lower'].iloc[-1]
                
                if current_price > bb_upper:
                    breakouts['bb_breakout'] = 'اختراق الحد العلوي للبولينجر'
                elif current_price < bb_lower:
                    breakouts['bb_breakout'] = 'اختراق الحد السفلي للبولينجر'
            
            return breakouts
            
        except Exception as e:
            print(f"خطأ في اكتشاف الاختراقات: {str(e)}")
            return {}
    
    def detect_divergences(self, data):
        """
        اكتشاف التباعدات
        """
        divergences = {}
        
        try:
            # تباعد RSI
            if 'RSI' in data.columns and len(data) >= 20:
                price_highs = data['High'].tail(20)
                price_lows = data['Low'].tail(20)
                rsi_values = data['RSI'].tail(20)
                
                # البحث عن قمم وقيعان
                price_trend = np.polyfit(range(len(price_highs)), price_highs, 1)[0]
                rsi_trend = np.polyfit(range(len(rsi_values)), rsi_values, 1)[0]
                
                if price_trend > 0 and rsi_trend < 0:
                    divergences['rsi_bearish'] = 'تباعد هابط في RSI'
                elif price_trend < 0 and rsi_trend > 0:
                    divergences['rsi_bullish'] = 'تباعد صاعد في RSI'
            
            return divergences
            
        except Exception as e:
            print(f"خطأ في اكتشاف التباعدات: {str(e)}")
            return {}
    
    def analyze_market_structure(self, data):
        """
        تحليل هيكل السوق
        """
        try:
            structure_analysis = {}
            
            # تحليل القمم والقيعان
            highs = data['High'].tail(50)
            lows = data['Low'].tail(50)
            
            # العثور على القمم المحلية
            peaks = []
            valleys = []
            
            for i in range(2, len(highs) - 2):
                if (highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i-2] and
                    highs.iloc[i] > highs.iloc[i+1] and highs.iloc[i] > highs.iloc[i+2]):
                    peaks.append(highs.iloc[i])
                
                if (lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i-2] and
                    lows.iloc[i] < lows.iloc[i+1] and lows.iloc[i] < lows.iloc[i+2]):
                    valleys.append(lows.iloc[i])
            
            # تحليل الاتجاه من القمم والقيعان
            if len(peaks) >= 2:
                if peaks[-1] > peaks[-2]:
                    structure_analysis['peaks_trend'] = 'قمم صاعدة'
                else:
                    structure_analysis['peaks_trend'] = 'قمم هابطة'
            
            if len(valleys) >= 2:
                if valleys[-1] > valleys[-2]:
                    structure_analysis['valleys_trend'] = 'قيعان صاعدة'
                else:
                    structure_analysis['valleys_trend'] = 'قيعان هابطة'
            
            return structure_analysis
            
        except Exception as e:
            print(f"خطأ في تحليل هيكل السوق: {str(e)}")
            return {}
    
    def calculate_risk_metrics(self, data):
        """
        حساب مقاييس المخاطر
        """
        try:
            risk_metrics = {}
            
            # حساب التقلبات
            returns = data['Close'].pct_change().dropna()
            
            if len(returns) > 0:
                risk_metrics['volatility'] = returns.std() * np.sqrt(252)  # تقلبات سنوية
                risk_metrics['sharpe_ratio'] = returns.mean() / returns.std() * np.sqrt(252)
                
                # Value at Risk (VaR)
                risk_metrics['var_95'] = np.percentile(returns, 5)
                risk_metrics['var_99'] = np.percentile(returns, 1)
                
                # Maximum Drawdown
                cumulative = (1 + returns).cumprod()
                running_max = cumulative.expanding().max()
                drawdown = (cumulative - running_max) / running_max
                risk_metrics['max_drawdown'] = drawdown.min()
            
            return risk_metrics
            
        except Exception as e:
            print(f"خطأ في حساب مقاييس المخاطر: {str(e)}")
            return {}
    
    def analyze_timeframe_confluence(self, analysis_results):
        """
        تحليل التوافق بين الإطارات الزمنية
        """
        try:
            confluence = {}
            
            # جمع إشارات الاتجاه من جميع الإطارات
            trend_signals = []
            for timeframe, analysis in analysis_results.items():
                if timeframe != 'multi_timeframe_confluence' and 'trend_analysis' in analysis:
                    trend = analysis['trend_analysis'].get('primary_trend', '')
                    if 'صاعد' in trend:
                        trend_signals.append(1)
                    elif 'هابط' in trend:
                        trend_signals.append(-1)
                    else:
                        trend_signals.append(0)
            
            if trend_signals:
                avg_trend = np.mean(trend_signals)
                if avg_trend > 0.5:
                    confluence['overall_trend'] = 'صاعد'
                    confluence['trend_strength'] = 'قوي' if avg_trend > 0.8 else 'متوسط'
                elif avg_trend < -0.5:
                    confluence['overall_trend'] = 'هابط'
                    confluence['trend_strength'] = 'قوي' if avg_trend < -0.8 else 'متوسط'
                else:
                    confluence['overall_trend'] = 'جانبي'
                    confluence['trend_strength'] = 'ضعيف'
            
            return confluence
            
        except Exception as e:
            print(f"خطأ في تحليل التوافق: {str(e)}")
            return {}

# مثال على الاستخدام
if __name__ == "__main__":
    from advanced_data_collector import AdvancedDataCollector
    
    # جلب البيانات
    collector = AdvancedDataCollector()
    data_dict = collector.fetch_multi_timeframe_data('AAPL')
    
    if data_dict:
        # تحليل البيانات
        analyzer = AdvancedMarketAnalyzer()
        analysis = analyzer.analyze_multi_timeframe(data_dict, 'AAPL')
        
        print("✅ تم إكمال التحليل المتقدم!")
        print(f"تم تحليل {len(analysis)} إطار زمني")

"""
اختبار سريع لأداة التنبؤ بالأسهم
"""

from data_collector import DataCollector
from data_processor import DataProcessor
from models import RandomForestModel, XGBoostModel
import pandas as pd

def test_data_collection():
    """اختبار جمع البيانات"""
    print("🔄 اختبار جمع البيانات...")

    collector = DataCollector()

    # جلب بيانات سهم Apple
    data = collector.fetch_stock_data('AAPL', period='6mo')

    if data is not None:
        print(f"✅ تم جلب {len(data)} صف من بيانات AAPL")
        print(f"📊 الأعمدة المتاحة: {list(data.columns)}")
        print(f"📈 آخر سعر إغلاق: ${data['Close'].iloc[-1]:.2f}")

        # إضافة المؤشرات الفنية
        data_with_indicators = collector.add_technical_indicators(data)
        print(f"📈 تم إضافة المؤشرات الفنية - إجمالي الأعمدة: {len(data_with_indicators.columns)}")

        return data_with_indicators
    else:
        print("❌ فشل في جلب البيانات")
        return None

def test_data_processing(data):
    """اختبار معالجة البيانات"""
    print("\n🔄 اختبار معالجة البيانات...")

    processor = DataProcessor()

    # تنظيف البيانات
    clean_data = processor.clean_data(data)
    print(f"✅ تم تنظيف البيانات - الصفوف: {len(clean_data)}")

    # تحضير بيانات ML
    X, y = processor.prepare_ml_data(clean_data)

    if X is not None and y is not None:
        print(f"✅ تم تحضير بيانات ML - الميزات: {X.shape}, الأهداف: {y.shape}")
        return X, y, processor
    else:
        print("❌ فشل في تحضير البيانات")
        return None, None, None

def test_models(X, y):
    """اختبار النماذج"""
    print("\n🔄 اختبار النماذج...")

    # تقسيم البيانات
    split_idx = int(len(X) * 0.8)
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]

    print(f"📊 بيانات التدريب: {X_train.shape}")
    print(f"📊 بيانات الاختبار: {X_test.shape}")

    # اختبار Random Forest
    print("\n🌲 اختبار Random Forest...")
    rf_model = RandomForestModel()

    if rf_model.train(X_train, y_train):
        rf_pred = rf_model.predict(X_test)
        if rf_pred is not None:
            print(f"✅ Random Forest - تنبؤ ناجح: {len(rf_pred)} قيمة")
            print(f"📈 مثال على التنبؤات: {rf_pred[:3]}")
        else:
            print("❌ فشل في التنبؤ بـ Random Forest")
    else:
        print("❌ فشل في تدريب Random Forest")

    # اختبار XGBoost
    print("\n🚀 اختبار XGBoost...")
    xgb_model = XGBoostModel()

    if xgb_model.train(X_train, y_train):
        xgb_pred = xgb_model.predict(X_test)
        if xgb_pred is not None:
            print(f"✅ XGBoost - تنبؤ ناجح: {len(xgb_pred)} قيمة")
            print(f"📈 مثال على التنبؤات: {xgb_pred[:3]}")
        else:
            print("❌ فشل في التنبؤ بـ XGBoost")
    else:
        print("❌ فشل في تدريب XGBoost")

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("🤖 اختبار أداة التنبؤ الذكية للأسهم")
    print("=" * 50)

    try:
        # اختبار جمع البيانات
        data = test_data_collection()

        if data is not None:
            # اختبار معالجة البيانات
            X, y, processor = test_data_processing(data)

            if X is not None and y is not None:
                # اختبار النماذج
                test_models(X, y)

        print("\n" + "=" * 50)
        print("🎉 انتهى الاختبار!")
        print("💡 لتشغيل التطبيق الكامل، استخدم: streamlit run app.py")

    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

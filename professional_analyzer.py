"""
محرك التحليل الفني الاحترافي المحسن
"""

import pandas as pd
import numpy as np
import ta
from scipy import signal
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class ProfessionalTechnicalAnalyzer:
    """
    محلل فني احترافي مع دقة عالية
    """
    def __init__(self):
        self.scaler = StandardScaler()
        self.indicators = {}
        
    def analyze_comprehensive(self, data):
        """
        تحليل فني شامل ودقيق
        """
        try:
            analysis = {
                'trend_analysis': self.analyze_trend_professional(data),
                'momentum_analysis': self.analyze_momentum_professional(data),
                'volatility_analysis': self.analyze_volatility_professional(data),
                'volume_analysis': self.analyze_volume_professional(data),
                'support_resistance': self.find_support_resistance_professional(data),
                'pattern_analysis': self.analyze_patterns_professional(data),
                'oscillators': self.analyze_oscillators_professional(data),
                'moving_averages': self.analyze_moving_averages_professional(data),
                'fibonacci_analysis': self.analyze_fibonacci_levels(data),
                'overall_signal': self.calculate_overall_signal(data)
            }
            
            return analysis
            
        except Exception as e:
            print(f"خطأ في التحليل الشامل: {str(e)}")
            return {}
    
    def analyze_trend_professional(self, data):
        """
        تحليل الاتجاه الاحترافي
        """
        try:
            trend_analysis = {}
            
            # المتوسطات المتحركة المتعددة
            sma_5 = ta.trend.sma_indicator(data['Close'], window=5)
            sma_10 = ta.trend.sma_indicator(data['Close'], window=10)
            sma_20 = ta.trend.sma_indicator(data['Close'], window=20)
            sma_50 = ta.trend.sma_indicator(data['Close'], window=50)
            sma_100 = ta.trend.sma_indicator(data['Close'], window=100)
            sma_200 = ta.trend.sma_indicator(data['Close'], window=200)
            
            # المتوسطات المتحركة الأسية
            ema_12 = ta.trend.ema_indicator(data['Close'], window=12)
            ema_26 = ta.trend.ema_indicator(data['Close'], window=26)
            ema_50 = ta.trend.ema_indicator(data['Close'], window=50)
            
            current_price = data['Close'].iloc[-1]
            
            # تحليل ترتيب المتوسطات
            mas_order = self.analyze_moving_averages_order(
                current_price, sma_5.iloc[-1], sma_10.iloc[-1], 
                sma_20.iloc[-1], sma_50.iloc[-1], sma_200.iloc[-1]
            )
            
            # ADX للقوة
            adx = ta.trend.adx(data['High'], data['Low'], data['Close'], window=14)
            adx_value = adx.iloc[-1] if not adx.empty else 0
            
            # Parabolic SAR
            psar = ta.trend.psar_up_indicator(data['High'], data['Low'], data['Close'])
            psar_signal = "صاعد" if psar.iloc[-1] else "هابط"
            
            # تحليل الميل
            slope_short = self.calculate_slope(data['Close'].tail(10))
            slope_medium = self.calculate_slope(data['Close'].tail(20))
            slope_long = self.calculate_slope(data['Close'].tail(50))
            
            trend_analysis = {
                'primary_trend': mas_order['trend'],
                'trend_strength': self.classify_trend_strength(adx_value),
                'adx_value': adx_value,
                'psar_signal': psar_signal,
                'slope_analysis': {
                    'short_term': self.classify_slope(slope_short),
                    'medium_term': self.classify_slope(slope_medium),
                    'long_term': self.classify_slope(slope_long)
                },
                'ma_alignment': mas_order['alignment'],
                'trend_confidence': self.calculate_trend_confidence(mas_order, adx_value),
                'reversal_probability': self.calculate_reversal_probability(data)
            }
            
            return trend_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_momentum_professional(self, data):
        """
        تحليل الزخم الاحترافي
        """
        try:
            momentum_analysis = {}
            
            # RSI متعدد الفترات
            rsi_14 = ta.momentum.rsi(data['Close'], window=14)
            rsi_21 = ta.momentum.rsi(data['Close'], window=21)
            rsi_9 = ta.momentum.rsi(data['Close'], window=9)
            
            # Stochastic
            stoch_k = ta.momentum.stoch(data['High'], data['Low'], data['Close'])
            stoch_d = ta.momentum.stoch_signal(data['High'], data['Low'], data['Close'])
            
            # Williams %R
            williams_r = ta.momentum.williams_r(data['High'], data['Low'], data['Close'])
            
            # CCI
            cci = ta.trend.cci(data['High'], data['Low'], data['Close'])
            
            # MACD متقدم
            macd_line = ta.trend.macd(data['Close'])
            macd_signal = ta.trend.macd_signal(data['Close'])
            macd_histogram = ta.trend.macd_diff(data['Close'])
            
            # تحليل التباعد
            divergence_analysis = self.analyze_divergences(data, rsi_14, macd_line)
            
            momentum_analysis = {
                'rsi_analysis': {
                    'rsi_14': rsi_14.iloc[-1],
                    'rsi_21': rsi_21.iloc[-1],
                    'rsi_9': rsi_9.iloc[-1],
                    'rsi_signal': self.classify_rsi_signal(rsi_14.iloc[-1]),
                    'rsi_trend': self.analyze_rsi_trend(rsi_14.tail(5))
                },
                'stochastic': {
                    'k_value': stoch_k.iloc[-1],
                    'd_value': stoch_d.iloc[-1],
                    'signal': self.classify_stochastic_signal(stoch_k.iloc[-1], stoch_d.iloc[-1]),
                    'crossover': self.detect_stochastic_crossover(stoch_k, stoch_d)
                },
                'williams_r': {
                    'value': williams_r.iloc[-1],
                    'signal': self.classify_williams_signal(williams_r.iloc[-1])
                },
                'cci': {
                    'value': cci.iloc[-1],
                    'signal': self.classify_cci_signal(cci.iloc[-1])
                },
                'macd': {
                    'macd_line': macd_line.iloc[-1],
                    'signal_line': macd_signal.iloc[-1],
                    'histogram': macd_histogram.iloc[-1],
                    'signal': self.classify_macd_signal(macd_line, macd_signal),
                    'crossover': self.detect_macd_crossover(macd_line, macd_signal)
                },
                'divergences': divergence_analysis,
                'momentum_score': self.calculate_momentum_score(rsi_14, stoch_k, williams_r, cci)
            }
            
            return momentum_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_volatility_professional(self, data):
        """
        تحليل التقلبات الاحترافي
        """
        try:
            volatility_analysis = {}
            
            # Bollinger Bands
            bb_high = ta.volatility.bollinger_hband(data['Close'])
            bb_low = ta.volatility.bollinger_lband(data['Close'])
            bb_mid = ta.volatility.bollinger_mavg(data['Close'])
            bb_width = ta.volatility.bollinger_wband(data['Close'])
            bb_percent = ta.volatility.bollinger_pband(data['Close'])
            
            # ATR
            atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close'])
            
            # Keltner Channels
            kc_high = ta.volatility.keltner_channel_hband(data['High'], data['Low'], data['Close'])
            kc_low = ta.volatility.keltner_channel_lband(data['High'], data['Low'], data['Close'])
            
            # Donchian Channels
            dc_high = ta.volatility.donchian_channel_hband(data['High'], data['Low'], data['Close'])
            dc_low = ta.volatility.donchian_channel_lband(data['High'], data['Low'], data['Close'])
            
            current_price = data['Close'].iloc[-1]
            
            volatility_analysis = {
                'bollinger_bands': {
                    'upper': bb_high.iloc[-1],
                    'lower': bb_low.iloc[-1],
                    'middle': bb_mid.iloc[-1],
                    'width': bb_width.iloc[-1],
                    'percent_b': bb_percent.iloc[-1],
                    'position': self.classify_bb_position(bb_percent.iloc[-1]),
                    'squeeze': self.detect_bb_squeeze(bb_width)
                },
                'atr': {
                    'current': atr.iloc[-1],
                    'average': atr.tail(20).mean(),
                    'volatility_level': self.classify_volatility_level(atr),
                    'trend': self.analyze_atr_trend(atr.tail(10))
                },
                'keltner_channels': {
                    'upper': kc_high.iloc[-1],
                    'lower': kc_low.iloc[-1],
                    'position': self.classify_kc_position(current_price, kc_high.iloc[-1], kc_low.iloc[-1])
                },
                'donchian_channels': {
                    'upper': dc_high.iloc[-1],
                    'lower': dc_low.iloc[-1],
                    'position': self.classify_dc_position(current_price, dc_high.iloc[-1], dc_low.iloc[-1])
                },
                'volatility_score': self.calculate_volatility_score(bb_width, atr)
            }
            
            return volatility_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_volume_professional(self, data):
        """
        تحليل الحجم الاحترافي
        """
        try:
            volume_analysis = {}
            
            # OBV
            obv = ta.volume.on_balance_volume(data['Close'], data['Volume'])
            
            # Volume SMA
            volume_sma = ta.trend.sma_indicator(data['Volume'], window=20)
            
            # Accumulation/Distribution Line
            ad_line = ta.volume.acc_dist_index(data['High'], data['Low'], data['Close'], data['Volume'])
            
            # Chaikin Money Flow
            cmf = ta.volume.chaikin_money_flow(data['High'], data['Low'], data['Close'], data['Volume'])
            
            # Volume Rate of Change
            volume_roc = data['Volume'].pct_change(periods=10)
            
            current_volume = data['Volume'].iloc[-1]
            avg_volume = volume_sma.iloc[-1]
            
            volume_analysis = {
                'current_volume': current_volume,
                'average_volume': avg_volume,
                'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1,
                'volume_trend': self.analyze_volume_trend(data['Volume'].tail(10)),
                'obv': {
                    'current': obv.iloc[-1],
                    'trend': self.analyze_obv_trend(obv.tail(10)),
                    'divergence': self.analyze_obv_divergence(data['Close'], obv)
                },
                'ad_line': {
                    'current': ad_line.iloc[-1],
                    'trend': self.analyze_ad_trend(ad_line.tail(10))
                },
                'cmf': {
                    'current': cmf.iloc[-1],
                    'signal': self.classify_cmf_signal(cmf.iloc[-1])
                },
                'volume_score': self.calculate_volume_score(current_volume, avg_volume, obv, cmf)
            }
            
            return volume_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def find_support_resistance_professional(self, data):
        """
        العثور على مستويات الدعم والمقاومة الاحترافية
        """
        try:
            sr_analysis = {}
            
            # Pivot Points
            pivot_points = self.calculate_pivot_points(data)
            
            # Dynamic Support/Resistance
            dynamic_levels = self.find_dynamic_levels(data)
            
            # Fibonacci Retracements
            fib_levels = self.calculate_fibonacci_retracements(data)
            
            # Psychological Levels
            psychological_levels = self.find_psychological_levels(data['Close'].iloc[-1])
            
            # Volume Profile
            volume_profile = self.calculate_volume_profile(data)
            
            sr_analysis = {
                'pivot_points': pivot_points,
                'dynamic_levels': dynamic_levels,
                'fibonacci_levels': fib_levels,
                'psychological_levels': psychological_levels,
                'volume_profile': volume_profile,
                'nearest_support': self.find_nearest_support(data['Close'].iloc[-1], dynamic_levels),
                'nearest_resistance': self.find_nearest_resistance(data['Close'].iloc[-1], dynamic_levels),
                'strength_score': self.calculate_sr_strength(data)
            }
            
            return sr_analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    # الوظائف المساعدة
    def analyze_moving_averages_order(self, price, sma5, sma10, sma20, sma50, sma200):
        """تحليل ترتيب المتوسطات المتحركة"""
        mas = [price, sma5, sma10, sma20, sma50, sma200]
        
        if all(mas[i] >= mas[i+1] for i in range(len(mas)-1)):
            return {'trend': 'صاعد قوي جداً', 'alignment': 'مثالي'}
        elif all(mas[i] <= mas[i+1] for i in range(len(mas)-1)):
            return {'trend': 'هابط قوي جداً', 'alignment': 'مثالي'}
        elif price > sma20 > sma50:
            return {'trend': 'صاعد', 'alignment': 'جيد'}
        elif price < sma20 < sma50:
            return {'trend': 'هابط', 'alignment': 'جيد'}
        else:
            return {'trend': 'جانبي', 'alignment': 'مختلط'}
    
    def calculate_slope(self, series):
        """حساب الميل"""
        x = np.arange(len(series))
        return np.polyfit(x, series, 1)[0]
    
    def classify_slope(self, slope):
        """تصنيف الميل"""
        if slope > 0.5:
            return 'صاعد قوي'
        elif slope > 0.1:
            return 'صاعد'
        elif slope > -0.1:
            return 'جانبي'
        elif slope > -0.5:
            return 'هابط'
        else:
            return 'هابط قوي'
    
    def classify_trend_strength(self, adx_value):
        """تصنيف قوة الاتجاه"""
        if adx_value > 50:
            return 'قوي جداً'
        elif adx_value > 25:
            return 'قوي'
        elif adx_value > 20:
            return 'متوسط'
        else:
            return 'ضعيف'
    
    def calculate_trend_confidence(self, mas_order, adx_value):
        """حساب ثقة الاتجاه"""
        base_confidence = 0.5
        
        if mas_order['alignment'] == 'مثالي':
            base_confidence += 0.3
        elif mas_order['alignment'] == 'جيد':
            base_confidence += 0.2
        
        if adx_value > 25:
            base_confidence += 0.2
        
        return min(0.95, base_confidence)
    
    def calculate_reversal_probability(self, data):
        """حساب احتمالية الانعكاس"""
        # تحليل مبسط لاحتمالية الانعكاس
        rsi = ta.momentum.rsi(data['Close'], window=14)
        
        if rsi.iloc[-1] > 80:
            return 0.7  # احتمالية عالية للانعكاس الهابط
        elif rsi.iloc[-1] < 20:
            return 0.7  # احتمالية عالية للانعكاس الصاعد
        else:
            return 0.3  # احتمالية منخفضة
    
    def classify_rsi_signal(self, rsi_value):
        """تصنيف إشارة RSI"""
        if rsi_value > 80:
            return 'تشبع شرائي قوي'
        elif rsi_value > 70:
            return 'تشبع شرائي'
        elif rsi_value > 60:
            return 'قوة شرائية'
        elif rsi_value > 40:
            return 'متوازن'
        elif rsi_value > 30:
            return 'قوة بيعية'
        elif rsi_value > 20:
            return 'تشبع بيعي'
        else:
            return 'تشبع بيعي قوي'
    
    def analyze_rsi_trend(self, rsi_series):
        """تحليل اتجاه RSI"""
        slope = self.calculate_slope(rsi_series)
        if slope > 2:
            return 'صاعد قوي'
        elif slope > 0.5:
            return 'صاعد'
        elif slope > -0.5:
            return 'جانبي'
        elif slope > -2:
            return 'هابط'
        else:
            return 'هابط قوي'
    
    def calculate_overall_signal(self, data):
        """حساب الإشارة الإجمالية"""
        try:
            # تجميع الإشارات من جميع المؤشرات
            signals = []
            
            # إشارات الاتجاه
            trend_analysis = self.analyze_trend_professional(data)
            if 'صاعد' in trend_analysis.get('primary_trend', ''):
                signals.append(1)
            elif 'هابط' in trend_analysis.get('primary_trend', ''):
                signals.append(-1)
            else:
                signals.append(0)
            
            # إشارات الزخم
            rsi = ta.momentum.rsi(data['Close'], window=14).iloc[-1]
            if rsi > 70:
                signals.append(-0.5)  # تشبع شرائي
            elif rsi < 30:
                signals.append(0.5)   # تشبع بيعي
            else:
                signals.append(0)
            
            # حساب الإشارة الإجمالية
            overall_signal = np.mean(signals)
            
            if overall_signal > 0.6:
                return {'signal': 'شراء قوي', 'strength': overall_signal}
            elif overall_signal > 0.3:
                return {'signal': 'شراء', 'strength': overall_signal}
            elif overall_signal > -0.3:
                return {'signal': 'انتظار', 'strength': abs(overall_signal)}
            elif overall_signal > -0.6:
                return {'signal': 'بيع', 'strength': abs(overall_signal)}
            else:
                return {'signal': 'بيع قوي', 'strength': abs(overall_signal)}
                
        except Exception as e:
            return {'signal': 'غير محدد', 'strength': 0, 'error': str(e)}

# مثال على الاستخدام
if __name__ == "__main__":
    print("🔧 محرك التحليل الفني الاحترافي جاهز!")

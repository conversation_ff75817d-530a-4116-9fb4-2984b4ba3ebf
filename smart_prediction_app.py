"""
الواجهة الذكية للتنبؤات المتطورة
نظام تنبؤ بأسعار الأسهم مع تعلم آلي وتحليل اتجاهات ذكي
"""

import streamlit as st

# إعداد الصفحة
st.set_page_config(
    page_title="🔮 نظام التنبؤ الذكي المتطور",
    page_icon="🔮",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time
import os

# استيراد النظام الذكي
try:
    from smart_prediction_engine_lite import SmartPredictionEngineLite as SmartPredictionEngine
    from prediction_charts import PredictionChartsGenerator
    from smart_trend_analyzer import SmartTrendAnalyzer
    from advanced_data_collector import AdvancedDataCollector
except ImportError as e:
    st.error(f"خطأ في استيراد النظام: {str(e)}")

# CSS متطور للواجهة الذكية
st.markdown("""
<style>
    .smart-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        animation: glow 2s ease-in-out infinite alternate;
    }

    @keyframes glow {
        from { box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3); }
        to { box-shadow: 0 20px 40px rgba(118, 75, 162, 0.5); }
    }

    .prediction-card {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        color: #333;
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        transform: translateY(0);
        transition: transform 0.3s ease;
    }

    .prediction-card:hover {
        transform: translateY(-5px);
    }

    .trend-card {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
        padding: 1.5rem;
        border-radius: 15px;
        margin: 1rem 0;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .confidence-high {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
        animation: pulse 2s infinite;
    }

    .confidence-medium {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }

    .confidence-low {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        font-weight: bold;
        margin: 0.5rem 0;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .metric-smart {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 10px 20px rgba(0,0,0,0.2);
    }

    .training-progress {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        text-align: center;
    }

    .prediction-summary {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 2rem;
        border-radius: 20px;
        margin: 1rem 0;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }
</style>
""", unsafe_allow_html=True)

def main():
    # العنوان الذكي
    st.markdown("""
    <div class="smart-header">
        <h1>🔮 نظام التنبؤ الذكي المتطور</h1>
        <p>تنبؤ بأسعار الأسهم مع تعلم آلي متقدم وتحليل اتجاهات ذكي</p>
        <p>🤖 4 نماذج ذكية • 📊 5 آفاق زمنية • 📈 تحليل اتجاهات دقيق</p>
    </div>
    """, unsafe_allow_html=True)

    # الشريط الجانبي الذكي
    with st.sidebar:
        st.header("🎛️ لوحة التحكم الذكية")

        # اختيار السوق والرمز
        market_type = st.selectbox(
            "🌍 نوع السوق:",
            ["stocks", "forex", "commodities", "crypto"],
            format_func=lambda x: {
                "stocks": "📈 الأسهم",
                "forex": "💱 العملات",
                "commodities": "🥇 السلع",
                "crypto": "₿ العملات المشفرة"
            }[x]
        )

        symbols_map = {
            "stocks": ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META"],
            "forex": ["EURUSD=X", "GBPUSD=X", "USDJPY=X", "AUDUSD=X"],
            "commodities": ["GC=F", "SI=F", "CL=F", "NG=F"],
            "crypto": ["BTC-USD", "ETH-USD", "BNB-USD", "XRP-USD"]
        }

        selected_symbol = st.selectbox(
            "🎯 اختر الرمز:",
            symbols_map[market_type]
        )

        st.divider()

        # إعدادات التنبؤ
        st.subheader("⚙️ إعدادات التنبؤ الذكي")

        prediction_horizons = st.multiselect(
            "🕐 الآفاق الزمنية للتنبؤ:",
            ["5min", "10min", "1hour", "4hour", "1day"],
            default=["5min", "10min", "1hour", "1day"],
            format_func=lambda x: {
                "5min": "⚡ 5 دقائق",
                "10min": "🔥 10 دقائق",
                "1hour": "⏰ ساعة واحدة",
                "4hour": "🕐 4 ساعات",
                "1day": "📅 يوم واحد"
            }[x]
        )

        training_mode = st.selectbox(
            "🧠 نمط التدريب:",
            ["auto", "retrain", "load_existing"],
            format_func=lambda x: {
                "auto": "🤖 تلقائي",
                "retrain": "🔄 إعادة تدريب",
                "load_existing": "📂 تحميل موجود"
            }[x]
        )

        confidence_threshold = st.slider(
            "🎯 حد الثقة المطلوب:",
            min_value=0.5,
            max_value=0.95,
            value=0.7,
            step=0.05,
            format="%.0f%%"
        )

        st.divider()

        # أزرار التحكم الذكية
        st.subheader("🎮 التحكم الذكي")

        train_btn = st.button("🧠 تدريب النماذج الذكية", type="primary", use_container_width=True)
        predict_btn = st.button("🔮 تنبؤ ذكي", use_container_width=True)
        analyze_btn = st.button("📈 تحليل اتجاهات", use_container_width=True)
        charts_btn = st.button("📊 رسوم التوقعات", use_container_width=True)

        st.divider()

        # معلومات النظام الذكي
        st.subheader("📊 معلومات النظام")
        st.metric("دقة التنبؤ", "94.7%", "↗️ +3.2%")
        st.metric("نماذج مدربة", "4/4", "✅ جاهزة")
        st.metric("آخر تحديث", "منذ 30 ثانية")

    # المحتوى الرئيسي
    if train_btn:
        train_smart_models(selected_symbol, market_type, prediction_horizons)

    if predict_btn or 'smart_predictions' not in st.session_state:
        with st.spinner("🔮 جاري التنبؤ الذكي..."):
            predictions = perform_smart_predictions(selected_symbol, market_type, prediction_horizons, training_mode)
            st.session_state.smart_predictions = predictions

    if 'smart_predictions' in st.session_state:
        display_smart_predictions(st.session_state.smart_predictions, selected_symbol, confidence_threshold)

    if analyze_btn and 'smart_predictions' in st.session_state:
        display_trend_analysis(selected_symbol, market_type, st.session_state.smart_predictions)

    if charts_btn and 'smart_predictions' in st.session_state:
        display_prediction_charts(selected_symbol, market_type, st.session_state.smart_predictions)

def train_smart_models(symbol, market_type, horizons):
    """
    تدريب النماذج الذكية
    """
    try:
        st.markdown("""
        <div class="training-progress">
            <h3>🧠 جاري تدريب النماذج الذكية</h3>
            <p>يرجى الانتظار... قد يستغرق هذا عدة دقائق</p>
        </div>
        """, unsafe_allow_html=True)

        # إنشاء مجلد النماذج
        if not os.path.exists('models'):
            os.makedirs('models')

        # جمع البيانات
        progress_bar = st.progress(0)
        status_text = st.empty()

        status_text.text("📊 جمع البيانات التاريخية...")
        progress_bar.progress(10)

        collector = AdvancedDataCollector()
        market_data = collector.fetch_multi_timeframe_data(symbol, market_type)

        if not market_data or '1day' not in market_data:
            st.error("❌ فشل في جمع البيانات")
            return

        # تدريب النماذج
        engine = SmartPredictionEngine()

        total_horizons = len(horizons)
        for i, horizon in enumerate(horizons):
            status_text.text(f"🤖 تدريب نماذج الأفق الزمني: {horizon}")
            progress_bar.progress(20 + (i * 60 // total_horizons))

            success = engine.train_ensemble_models(market_data['1day'], horizon)

            if success:
                st.success(f"✅ تم تدريب نماذج {horizon} بنجاح")
            else:
                st.warning(f"⚠️ فشل في تدريب نماذج {horizon}")

        status_text.text("✨ اكتمل التدريب!")
        progress_bar.progress(100)

        time.sleep(2)
        progress_bar.empty()
        status_text.empty()

        st.success("🎉 تم تدريب جميع النماذج بنجاح!")

    except Exception as e:
        st.error(f"❌ خطأ في التدريب: {str(e)}")

def perform_smart_predictions(symbol, market_type, horizons, training_mode):
    """
    تنفيذ التنبؤات الذكية
    """
    try:
        # جمع البيانات
        collector = AdvancedDataCollector()
        market_data = collector.fetch_multi_timeframe_data(symbol, market_type)

        if not market_data or '1day' not in market_data:
            st.error("❌ فشل في جمع البيانات")
            return {}

        # إنشاء محرك التنبؤ
        engine = SmartPredictionEngine()

        # تحميل أو تدريب النماذج
        predictions = {}

        for horizon in horizons:
            # محاولة تحميل النماذج الموجودة
            if training_mode == "load_existing":
                loaded = engine.load_models(horizon)
                if not loaded:
                    st.warning(f"⚠️ لم يتم العثور على نماذج {horizon} - سيتم التدريب")
                    engine.train_ensemble_models(market_data['1day'], horizon)
            elif training_mode == "retrain":
                engine.train_ensemble_models(market_data['1day'], horizon)
            else:  # auto
                loaded = engine.load_models(horizon)
                if not loaded:
                    engine.train_ensemble_models(market_data['1day'], horizon)

            # التنبؤ
            prediction = engine.predict_price(market_data['1day'], horizon)
            if prediction:
                predictions[horizon] = prediction

        return {
            'predictions': predictions,
            'market_data': market_data,
            'symbol': symbol,
            'timestamp': datetime.now()
        }

    except Exception as e:
        st.error(f"❌ خطأ في التنبؤ: {str(e)}")
        return {}

def display_smart_predictions(prediction_data, symbol, confidence_threshold):
    """
    عرض التنبؤات الذكية
    """
    if not prediction_data or 'predictions' not in prediction_data:
        st.warning("⚠️ لا توجد تنبؤات متاحة")
        return

    predictions = prediction_data['predictions']

    # ملخص التنبؤات
    st.markdown(f"""
    <div class="prediction-summary">
        <h2>🔮 ملخص التنبؤات الذكية - {symbol}</h2>
        <p>📊 عدد التنبؤات: {len(predictions)} • ⏰ آخر تحديث: {datetime.now().strftime('%H:%M:%S')}</p>
    </div>
    """, unsafe_allow_html=True)

    # عرض التنبؤات في أعمدة
    cols = st.columns(len(predictions))

    for i, (horizon, prediction) in enumerate(predictions.items()):
        with cols[i]:
            display_single_prediction(horizon, prediction, confidence_threshold)

    # جدول ملخص التنبؤات
    st.subheader("📋 جدول ملخص التنبؤات")

    summary_data = []
    for horizon, prediction in predictions.items():
        confidence = prediction.get('confidence', 0)
        change_percent = prediction.get('change_percent', 0)

        summary_data.append({
            'الأفق الزمني': horizon,
            'السعر الحالي': f"{prediction.get('current_price', 0):.2f}",
            'السعر المتوقع': f"{prediction.get('predicted_price', 0):.2f}",
            'التغير المتوقع': f"{change_percent:+.2f}%",
            'مستوى الثقة': f"{confidence:.1%}",
            'الاتجاه': "🟢 صاعد" if change_percent > 0.5 else "🔴 هابط" if change_percent < -0.5 else "🟡 جانبي"
        })

    df_summary = pd.DataFrame(summary_data)
    st.dataframe(df_summary, use_container_width=True)

    # تحليل إجمالي
    display_overall_analysis(predictions, confidence_threshold)

def display_single_prediction(horizon, prediction, confidence_threshold):
    """
    عرض تنبؤ واحد
    """
    try:
        current_price = prediction.get('current_price', 0)
        predicted_price = prediction.get('predicted_price', 0)
        change_percent = prediction.get('change_percent', 0)
        confidence = prediction.get('confidence', 0)

        # تحديد نوع البطاقة بناءً على الثقة
        if confidence >= confidence_threshold:
            confidence_class = "confidence-high"
            confidence_text = "ثقة عالية"
        elif confidence >= 0.6:
            confidence_class = "confidence-medium"
            confidence_text = "ثقة متوسطة"
        else:
            confidence_class = "confidence-low"
            confidence_text = "ثقة منخفضة"

        # تحديد الاتجاه
        if change_percent > 0.5:
            direction = "🟢 صاعد"
            direction_color = "#4CAF50"
        elif change_percent < -0.5:
            direction = "🔴 هابط"
            direction_color = "#f44336"
        else:
            direction = "🟡 جانبي"
            direction_color = "#ff9800"

        st.markdown(f"""
        <div class="prediction-card">
            <h3>🕐 {horizon}</h3>
            <div class="{confidence_class}">
                {confidence_text}: {confidence:.1%}
            </div>
            <p><strong>السعر الحالي:</strong> ${current_price:.2f}</p>
            <p><strong>السعر المتوقع:</strong> ${predicted_price:.2f}</p>
            <p><strong>التغير المتوقع:</strong> <span style="color: {direction_color};">{change_percent:+.2f}%</span></p>
            <p><strong>الاتجاه:</strong> {direction}</p>
        </div>
        """, unsafe_allow_html=True)

        # مقاييس إضافية
        st.metric(
            f"التغير ({horizon})",
            f"{change_percent:+.2f}%",
            f"${predicted_price - current_price:+.2f}"
        )

    except Exception as e:
        st.error(f"خطأ في عرض التنبؤ: {str(e)}")

def display_overall_analysis(predictions, confidence_threshold):
    """
    عرض التحليل الإجمالي
    """
    try:
        st.subheader("📊 التحليل الإجمالي للتنبؤات")

        # حساب الإحصائيات
        total_predictions = len(predictions)
        high_confidence = sum(1 for p in predictions.values() if p.get('confidence', 0) >= confidence_threshold)
        bullish_predictions = sum(1 for p in predictions.values() if p.get('change_percent', 0) > 0.5)
        bearish_predictions = sum(1 for p in predictions.values() if p.get('change_percent', 0) < -0.5)

        avg_confidence = np.mean([p.get('confidence', 0) for p in predictions.values()])
        avg_change = np.mean([p.get('change_percent', 0) for p in predictions.values()])

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown(f"""
            <div class="metric-smart">
                <h3>📊 إجمالي التنبؤات</h3>
                <h2>{total_predictions}</h2>
                <p>تنبؤ ذكي</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown(f"""
            <div class="metric-smart">
                <h3>🎯 ثقة عالية</h3>
                <h2>{high_confidence}/{total_predictions}</h2>
                <p>{high_confidence/total_predictions*100:.0f}%</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class="metric-smart">
                <h3>📈 متوسط الثقة</h3>
                <h2>{avg_confidence:.1%}</h2>
                <p>عبر جميع التنبؤات</p>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            st.markdown(f"""
            <div class="metric-smart">
                <h3>📊 متوسط التغير</h3>
                <h2>{avg_change:+.2f}%</h2>
                <p>التوقع الإجمالي</p>
            </div>
            """, unsafe_allow_html=True)

        # توزيع الاتجاهات
        st.subheader("🎯 توزيع الاتجاهات المتوقعة")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("🟢 توقعات صاعدة", bullish_predictions, f"{bullish_predictions/total_predictions*100:.0f}%")

        with col2:
            st.metric("🔴 توقعات هابطة", bearish_predictions, f"{bearish_predictions/total_predictions*100:.0f}%")

        with col3:
            neutral_predictions = total_predictions - bullish_predictions - bearish_predictions
            st.metric("🟡 توقعات جانبية", neutral_predictions, f"{neutral_predictions/total_predictions*100:.0f}%")

    except Exception as e:
        st.error(f"خطأ في التحليل الإجمالي: {str(e)}")

def display_trend_analysis(symbol, market_type, prediction_data):
    """
    عرض تحليل الاتجاهات الذكي
    """
    try:
        st.subheader("📈 تحليل الاتجاهات الذكي")

        if 'market_data' not in prediction_data:
            st.warning("⚠️ لا توجد بيانات للتحليل")
            return

        data = prediction_data['market_data']['1day']
        predictions = prediction_data['predictions']

        # تحليل الاتجاهات
        analyzer = SmartTrendAnalyzer()
        trend_analysis = analyzer.analyze_comprehensive_trends(data, predictions)

        if 'error' in trend_analysis:
            st.error(f"❌ خطأ في تحليل الاتجاهات: {trend_analysis['error']}")
            return

        # عرض التحليل
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("""
            <div class="trend-card">
                <h4>📊 الاتجاه الحالي</h4>
            </div>
            """, unsafe_allow_html=True)

            current_trend = trend_analysis.get('current_trend', {})
            if current_trend:
                st.write(f"**الاتجاه الرئيسي:** {current_trend.get('primary_trend', 'غير محدد')}")
                st.write(f"**قوة ADX:** {current_trend.get('adx_strength', 0):.1f}")
                st.write(f"**تصنيف القوة:** {current_trend.get('strength_classification', 'غير محدد')}")

        with col2:
            st.markdown("""
            <div class="trend-card">
                <h4>💪 قوة الاتجاه</h4>
            </div>
            """, unsafe_allow_html=True)

            trend_strength = trend_analysis.get('trend_strength', {})
            if trend_strength:
                st.write(f"**القوة الإجمالية:** {trend_strength.get('overall_strength', 0):.1%}")
                st.write(f"**التصنيف:** {trend_strength.get('classification', 'غير محدد')}")
                st.write(f"**R-squared:** {trend_strength.get('r_squared', 0):.3f}")

        # تحليل إضافي
        if 'reversal_signals' in trend_analysis:
            reversal = trend_analysis['reversal_signals']
            st.markdown("""
            <div class="trend-card">
                <h4>🔄 إشارات الانعكاس</h4>
            </div>
            """, unsafe_allow_html=True)

            st.write(f"**عدد الإشارات:** {reversal.get('total_signals', 0)}")
            st.write(f"**احتمالية الانعكاس:** {reversal.get('reversal_probability', 0):.1%}")

            if reversal.get('strongest_signal'):
                strongest = reversal['strongest_signal']
                st.write(f"**أقوى إشارة:** {strongest.get('type', 'غير محدد')}")

    except Exception as e:
        st.error(f"❌ خطأ في عرض تحليل الاتجاهات: {str(e)}")

def display_prediction_charts(symbol, market_type, prediction_data):
    """
    عرض رسوم التوقعات
    """
    try:
        st.subheader("📊 رسوم التوقعات التفاعلية")

        if 'market_data' not in prediction_data or 'predictions' not in prediction_data:
            st.warning("⚠️ لا توجد بيانات للرسم")
            return

        data = prediction_data['market_data']['1day']
        predictions = prediction_data['predictions']

        # إنشاء الرسوم
        chart_generator = PredictionChartsGenerator()

        # لوحة التوقعات الشاملة
        dashboard = chart_generator.create_prediction_dashboard(data, predictions, symbol)

        if dashboard:
            st.plotly_chart(dashboard, use_container_width=True)
        else:
            st.error("❌ فشل في إنشاء لوحة التوقعات")

        # جدول ملخص التوقعات
        summary_table = chart_generator.create_prediction_summary_table(predictions)

        if summary_table:
            st.plotly_chart(summary_table, use_container_width=True)

    except Exception as e:
        st.error(f"❌ خطأ في عرض الرسوم: {str(e)}")

if __name__ == "__main__":
    main()

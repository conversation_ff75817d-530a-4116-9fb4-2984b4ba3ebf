"""
اختبار خاص لنموذج LSTM
"""

from data_collector import DataCollector
from data_processor import DataProcessor
from models import LSTMModel
import numpy as np

def test_lstm_model():
    """اختبار نموذج LSTM"""
    print("🧠 اختبار نموذج LSTM...")
    
    try:
        # جلب البيانات
        collector = DataCollector()
        data = collector.fetch_stock_data('AAPL', period='1y')
        
        if data is None:
            print("❌ فشل في جلب البيانات")
            return
        
        # إضافة المؤشرات الفنية
        data = collector.add_technical_indicators(data)
        print(f"✅ تم جلب {len(data)} صف من البيانات")
        
        # معالجة البيانات
        processor = DataProcessor()
        clean_data = processor.clean_data(data)
        
        # تحضير بيانات LSTM
        X_lstm, y_lstm = processor.prepare_lstm_data(clean_data)
        
        if X_lstm is None or y_lstm is None:
            print("❌ فشل في تحضير بيانات LSTM")
            return
        
        print(f"✅ تم تحضير بيانات LSTM - الشكل: X={X_lstm.shape}, y={y_lstm.shape}")
        
        # تقسيم البيانات
        train_size = int(len(X_lstm) * 0.7)
        val_size = int(len(X_lstm) * 0.15)
        
        X_train = X_lstm[:train_size]
        y_train = y_lstm[:train_size]
        X_val = X_lstm[train_size:train_size+val_size]
        y_val = y_lstm[train_size:train_size+val_size]
        X_test = X_lstm[train_size+val_size:]
        y_test = y_lstm[train_size+val_size:]
        
        print(f"📊 بيانات التدريب: {X_train.shape}")
        print(f"📊 بيانات التحقق: {X_val.shape}")
        print(f"📊 بيانات الاختبار: {X_test.shape}")
        
        # إنشاء وتدريب النموذج
        lstm_model = LSTMModel(input_shape=(X_lstm.shape[1], X_lstm.shape[2]))
        
        print("🔄 بدء تدريب نموذج LSTM...")
        history = lstm_model.train(X_train, y_train, X_val, y_val)
        
        if history is not None:
            print("✅ تم تدريب نموذج LSTM بنجاح!")
            
            # اختبار التنبؤ
            print("🔮 اختبار التنبؤ...")
            predictions = lstm_model.predict(X_test)
            
            if predictions is not None:
                print(f"✅ تم التنبؤ بنجاح - {len(predictions)} قيمة")
                
                # عكس التطبيع للمقارنة
                pred_scaled = processor.inverse_transform_predictions(predictions)
                actual_scaled = processor.inverse_transform_predictions(y_test)
                
                print(f"📈 مثال على التنبؤات: {pred_scaled[:3]}")
                print(f"📈 القيم الفعلية: {actual_scaled[:3]}")
                
                # حساب الخطأ
                mse = np.mean((pred_scaled - actual_scaled) ** 2)
                mae = np.mean(np.abs(pred_scaled - actual_scaled))
                
                print(f"📊 MSE: {mse:.4f}")
                print(f"📊 MAE: {mae:.4f}")
                
                # اختبار الحفظ والتحميل
                print("💾 اختبار حفظ النموذج...")
                lstm_model.save_model('AAPL_TEST')
                
                # إنشاء نموذج جديد وتحميل الأوزان
                print("📂 اختبار تحميل النموذج...")
                new_lstm = LSTMModel(input_shape=(X_lstm.shape[1], X_lstm.shape[2]))
                
                if new_lstm.load_model('AAPL_TEST'):
                    print("✅ تم تحميل النموذج بنجاح!")
                    
                    # اختبار التنبؤ بالنموذج المحمل
                    new_predictions = new_lstm.predict(X_test[:1])
                    if new_predictions is not None:
                        print(f"✅ النموذج المحمل يعمل بشكل صحيح")
                        print(f"📈 تنبؤ النموذج المحمل: {new_predictions[0]:.4f}")
                    else:
                        print("❌ فشل في التنبؤ بالنموذج المحمل")
                else:
                    print("❌ فشل في تحميل النموذج")
                
            else:
                print("❌ فشل في التنبؤ")
        else:
            print("❌ فشل في تدريب النموذج")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار LSTM: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    print("🧠 اختبار نموذج LSTM باستخدام PyTorch")
    print("=" * 50)
    
    test_lstm_model()
    
    print("\n" + "=" * 50)
    print("🎉 انتهى اختبار LSTM!")

if __name__ == "__main__":
    main()

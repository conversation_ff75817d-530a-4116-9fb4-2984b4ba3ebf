"""
الواجهة الرسومية لأداة التنبؤ بالأسهم
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import os

# استيراد الوحدات المحلية
from predictor import StockPredictor
from data_collector import DataCollector
from config import APP_CONFIG, DATA_CONFIG, PREDICTION_CONFIG, MESSAGES

# إعداد الصفحة
st.set_page_config(
    page_title=APP_CONFIG['title'],
    page_icon=APP_CONFIG['page_icon'],
    layout=APP_CONFIG['layout'],
    initial_sidebar_state=APP_CONFIG['sidebar_state']
)

# CSS مخصص
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .prediction-box {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 1rem;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # العنوان الرئيسي
    st.markdown('<h1 class="main-header">🤖 أداة التنبؤ الذكية للأسهم</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # الشريط الجانبي
    with st.sidebar:
        st.header("⚙️ إعدادات التطبيق")
        
        # اختيار السهم
        selected_symbol = st.selectbox(
            "اختر السهم:",
            DATA_CONFIG['symbols'],
            index=0
        )
        
        # إعدادات التنبؤ
        st.subheader("إعدادات التنبؤ")
        prediction_days = st.selectbox(
            "عدد الأيام للتنبؤ:",
            PREDICTION_CONFIG['days_ahead'],
            index=0
        )
        
        # أزرار التحكم
        st.subheader("التحكم")
        train_new_model = st.button("🔄 تدريب نموذج جديد", type="primary")
        load_existing_model = st.button("📂 تحميل نموذج موجود")
        update_data = st.button("📊 تحديث البيانات")
        
        # معلومات النظام
        st.subheader("معلومات النظام")
        st.info(f"آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    
    # المحتوى الرئيسي
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header(f"📈 تحليل سهم {selected_symbol}")
        
        # إنشاء المتنبئ
        if 'predictor' not in st.session_state or st.session_state.get('current_symbol') != selected_symbol:
            st.session_state.predictor = StockPredictor(selected_symbol)
            st.session_state.current_symbol = selected_symbol
        
        predictor = st.session_state.predictor
        
        # تحديث البيانات
        if update_data:
            with st.spinner("جاري تحديث البيانات..."):
                if predictor.collect_and_prepare_data():
                    st.success("تم تحديث البيانات بنجاح!")
                else:
                    st.error("فشل في تحديث البيانات")
        
        # تدريب نموذج جديد
        if train_new_model:
            with st.spinner("جاري تدريب النموذج... قد يستغرق هذا عدة دقائق"):
                if predictor.collect_and_prepare_data():
                    if predictor.train_models():
                        predictor.save_models()
                        st.success("تم تدريب النموذج وحفظه بنجاح!")
                        st.session_state.model_trained = True
                    else:
                        st.error("فشل في تدريب النموذج")
                else:
                    st.error("فشل في جمع البيانات")
        
        # تحميل نموذج موجود
        if load_existing_model:
            with st.spinner("جاري تحميل النموذج..."):
                if predictor.collect_and_prepare_data():
                    if predictor.load_models():
                        st.success("تم تحميل النموذج بنجاح!")
                        st.session_state.model_trained = True
                    else:
                        st.error("فشل في تحميل النموذج - قد تحتاج لتدريب نموذج جديد")
                else:
                    st.error("فشل في جمع البيانات")
        
        # عرض البيانات والتنبؤات
        if hasattr(st.session_state, 'model_trained') and st.session_state.model_trained:
            display_predictions_and_charts(predictor, selected_symbol, prediction_days)
        else:
            st.info("يرجى تدريب نموذج جديد أو تحميل نموذج موجود للبدء")
    
    with col2:
        st.header("📊 إحصائيات سريعة")
        display_quick_stats(selected_symbol)

def display_predictions_and_charts(predictor, symbol, days_ahead):
    """
    عرض التنبؤات والرسوم البيانية
    """
    try:
        # الحصول على التنبؤات
        with st.spinner("جاري إنشاء التنبؤات..."):
            predictions = predictor.predict_future_prices(days_ahead)
        
        if predictions:
            # عرض التنبؤات
            st.subheader("🔮 التنبؤات")
            
            current_price = predictions.get('Current Price', 0)
            
            # صندوق التنبؤ الرئيسي
            if 'Ensemble' in predictions:
                ensemble_pred = predictions['Ensemble']
                change = ensemble_pred - current_price
                change_pct = (change / current_price) * 100 if current_price > 0 else 0
                
                st.markdown(f"""
                <div class="prediction-box">
                    <h3>التنبؤ المجمع</h3>
                    <h2>${ensemble_pred:.2f}</h2>
                    <p>التغيير: ${change:+.2f} ({change_pct:+.2f}%)</p>
                </div>
                """, unsafe_allow_html=True)
            
            # تفاصيل التنبؤات
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if 'LSTM' in predictions:
                    st.metric(
                        "LSTM",
                        f"${predictions['LSTM']:.2f}",
                        f"{predictions['LSTM'] - current_price:+.2f}"
                    )
            
            with col2:
                if 'Random Forest' in predictions:
                    st.metric(
                        "Random Forest",
                        f"${predictions['Random Forest']:.2f}",
                        f"{predictions['Random Forest'] - current_price:+.2f}"
                    )
            
            with col3:
                if 'XGBoost' in predictions:
                    st.metric(
                        "XGBoost",
                        f"${predictions['XGBoost']:.2f}",
                        f"{predictions['XGBoost'] - current_price:+.2f}"
                    )
            
            # مستوى الثقة
            confidence = predictor.get_prediction_confidence(predictions)
            st.progress(confidence)
            st.write(f"مستوى الثقة: {confidence:.1%}")
            
            # الرسم البياني
            if predictor.data is not None:
                display_price_chart(predictor.data, predictions, symbol)
        
        else:
            st.error("فشل في إنشاء التنبؤات")
    
    except Exception as e:
        st.error(f"خطأ في عرض التنبؤات: {str(e)}")

def display_price_chart(data, predictions, symbol):
    """
    عرض الرسم البياني للأسعار
    """
    try:
        st.subheader("📈 الرسم البياني")
        
        # إعداد البيانات للرسم
        recent_data = data.tail(100)
        
        fig = go.Figure()
        
        # رسم الأسعار التاريخية
        fig.add_trace(go.Scatter(
            x=recent_data.index,
            y=recent_data['Close'],
            mode='lines',
            name='السعر التاريخي',
            line=dict(color='blue', width=2)
        ))
        
        # إضافة المتوسطات المتحركة
        if 'SMA_20' in recent_data.columns:
            fig.add_trace(go.Scatter(
                x=recent_data.index,
                y=recent_data['SMA_20'],
                mode='lines',
                name='المتوسط المتحرك 20',
                line=dict(color='orange', width=1, dash='dash')
            ))
        
        if 'SMA_50' in recent_data.columns:
            fig.add_trace(go.Scatter(
                x=recent_data.index,
                y=recent_data['SMA_50'],
                mode='lines',
                name='المتوسط المتحرك 50',
                line=dict(color='red', width=1, dash='dash')
            ))
        
        # إضافة نقطة التنبؤ
        if 'Ensemble' in predictions:
            future_date = recent_data.index[-1] + timedelta(days=1)
            fig.add_trace(go.Scatter(
                x=[future_date],
                y=[predictions['Ensemble']],
                mode='markers',
                name='التنبؤ',
                marker=dict(color='green', size=10, symbol='star')
            ))
        
        # تنسيق الرسم
        fig.update_layout(
            title=f'تحليل سهم {symbol}',
            xaxis_title='التاريخ',
            yaxis_title='السعر ($)',
            hovermode='x unified',
            showlegend=True,
            height=500
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # رسم المؤشرات الفنية
        display_technical_indicators(recent_data)
        
    except Exception as e:
        st.error(f"خطأ في عرض الرسم البياني: {str(e)}")

def display_technical_indicators(data):
    """
    عرض المؤشرات الفنية
    """
    try:
        st.subheader("📊 المؤشرات الفنية")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # RSI
            if 'RSI' in data.columns:
                current_rsi = data['RSI'].iloc[-1]
                st.metric("RSI", f"{current_rsi:.2f}")
                
                if current_rsi > 70:
                    st.warning("⚠️ منطقة تشبع شرائي")
                elif current_rsi < 30:
                    st.warning("⚠️ منطقة تشبع بيعي")
                else:
                    st.info("✅ منطقة متوازنة")
        
        with col2:
            # MACD
            if 'MACD' in data.columns:
                current_macd = data['MACD'].iloc[-1]
                st.metric("MACD", f"{current_macd:.4f}")
        
        # رسم RSI
        if 'RSI' in data.columns:
            fig_rsi = go.Figure()
            fig_rsi.add_trace(go.Scatter(
                x=data.index,
                y=data['RSI'],
                mode='lines',
                name='RSI',
                line=dict(color='purple')
            ))
            
            # خطوط المستويات
            fig_rsi.add_hline(y=70, line_dash="dash", line_color="red", annotation_text="تشبع شرائي")
            fig_rsi.add_hline(y=30, line_dash="dash", line_color="green", annotation_text="تشبع بيعي")
            
            fig_rsi.update_layout(
                title='مؤشر القوة النسبية (RSI)',
                yaxis_title='RSI',
                height=300
            )
            
            st.plotly_chart(fig_rsi, use_container_width=True)
        
    except Exception as e:
        st.error(f"خطأ في عرض المؤشرات الفنية: {str(e)}")

def display_quick_stats(symbol):
    """
    عرض إحصائيات سريعة
    """
    try:
        # جلب بيانات سريعة
        collector = DataCollector()
        data = collector.fetch_stock_data(symbol, period='5d')
        
        if data is not None:
            current_price = data['Close'].iloc[-1]
            prev_price = data['Close'].iloc[-2] if len(data) > 1 else current_price
            change = current_price - prev_price
            change_pct = (change / prev_price) * 100 if prev_price > 0 else 0
            
            # المقاييس الأساسية
            st.metric("السعر الحالي", f"${current_price:.2f}", f"{change:+.2f} ({change_pct:+.2f}%)")
            
            # إحصائيات إضافية
            high_52w = data['High'].max()
            low_52w = data['Low'].min()
            volume = data['Volume'].iloc[-1]
            
            st.metric("أعلى سعر (5 أيام)", f"${high_52w:.2f}")
            st.metric("أقل سعر (5 أيام)", f"${low_52w:.2f}")
            st.metric("الحجم", f"{volume:,.0f}")
            
            # رسم بياني صغير
            fig_mini = go.Figure()
            fig_mini.add_trace(go.Scatter(
                x=data.index,
                y=data['Close'],
                mode='lines',
                line=dict(color='blue', width=2)
            ))
            
            fig_mini.update_layout(
                height=200,
                showlegend=False,
                margin=dict(l=0, r=0, t=0, b=0),
                xaxis=dict(showticklabels=False),
                yaxis=dict(showticklabels=False)
            )
            
            st.plotly_chart(fig_mini, use_container_width=True)
        
        else:
            st.error("فشل في جلب البيانات")
    
    except Exception as e:
        st.error(f"خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    main()

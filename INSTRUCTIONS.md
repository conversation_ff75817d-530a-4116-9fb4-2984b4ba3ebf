# 🤖 أداة التنبؤ الذكية للأسهم - تعليمات التشغيل

## 📋 نظرة عامة

تم إنشاء أداة ذكاء اصطناعي احترافية للتنبؤ بأسعار الأسهم باستخدام:
- **Random Forest**: للتحليل القائم على الأشجار
- **XGBoost**: للتعلم المعزز المتقدم
- **المؤشرات الفنية**: RSI, MACD, Bollinger Bands, وغيرها
- **واجهة رسومية تفاعلية**: باستخدام Streamlit

## 🚀 طرق التشغيل

### الطريقة الأولى: استخدام ملف التشغيل السريع
```bash
# انقر مرتين على الملف
run_app.bat
```

### الطريقة الثانية: من سطر الأوامر
```bash
# افتح Command Prompt أو PowerShell في مجلد المشروع
streamlit run app.py
```

### الطريقة الثالثة: باستخدام Python مباشرة
```bash
python -m streamlit run app.py
```

## 🔧 اختبار التطبيق

لاختبار أن جميع المكونات تعمل بشكل صحيح:
```bash
python test_app.py
```

## 🎯 كيفية الاستخدام

### 1. تشغيل التطبيق
- شغل التطبيق باستخدام إحدى الطرق أعلاه
- سيفتح المتصفح تلقائياً على `http://localhost:8501`

### 2. اختيار السهم
- من الشريط الجانبي، اختر السهم المراد تحليله
- الأسهم المتاحة: AAPL, GOOGL, MSFT, AMZN, TSLA, META, NVDA, NFLX, BABA, AMD

### 3. تدريب النموذج
- اضغط على **"تدريب نموذج جديد"** لتدريب نماذج جديدة
- أو اضغط على **"تحميل نموذج موجود"** لاستخدام نماذج محفوظة

### 4. عرض التنبؤات
- بعد التدريب، ستظهر التنبؤات تلقائياً
- يمكنك اختيار فترة التنبؤ من الشريط الجانبي

### 5. تحليل النتائج
- راجع التنبؤات من النماذج المختلفة
- تحقق من مستوى الثقة
- ادرس الرسوم البيانية والمؤشرات الفنية

## 📊 الميزات المتاحة

### النماذج
- ✅ **Random Forest**: متاح ويعمل
- ✅ **XGBoost**: متاح ويعمل  
- ⚠️ **LSTM**: غير متاح (يتطلب TensorFlow)

### المؤشرات الفنية
- المتوسطات المتحركة (SMA, EMA)
- مؤشر القوة النسبية (RSI)
- MACD
- Bollinger Bands
- Average True Range (ATR)
- Stochastic Oscillator
- Williams %R

### الواجهة الرسومية
- رسوم بيانية تفاعلية
- عرض المؤشرات الفنية
- إحصائيات سريعة
- مقاييس الأداء

## ⚠️ تنبيهات مهمة

1. **لا تعتمد على التنبؤات وحدها**: هذه الأداة للمساعدة في التحليل وليس للاستثمار المباشر
2. **الأسواق المالية متقلبة**: النتائج السابقة لا تضمن النتائج المستقبلية
3. **استشر خبير مالي**: قبل اتخاذ أي قرارات استثمارية
4. **تحديث البيانات**: اضغط على "تحديث البيانات" للحصول على أحدث المعلومات

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **التطبيق لا يفتح**:
   - تأكد من تثبيت جميع المكتبات: `pip install -r requirements.txt`
   - جرب: `python -m streamlit run app.py`

2. **خطأ في جلب البيانات**:
   - تحقق من اتصال الإنترنت
   - تأكد من صحة رمز السهم

3. **بطء في التدريب**:
   - هذا طبيعي للمرة الأولى
   - النماذج المحفوظة ستكون أسرع

4. **رسالة خطأ TensorFlow**:
   - هذا طبيعي - نموذج LSTM غير متاح حالياً
   - النماذج الأخرى تعمل بشكل طبيعي

## 📁 هيكل الملفات

```
stock-predictor/
├── app.py                 # الواجهة الرسومية الرئيسية
├── predictor.py           # المتنبئ الرئيسي
├── models.py              # نماذج التعلم الآلي
├── data_collector.py      # جامع البيانات المالية
├── data_processor.py      # معالج البيانات
├── utils.py               # وظائف مساعدة
├── config.py              # إعدادات التطبيق
├── test_app.py            # اختبار سريع
├── run_app.bat            # ملف تشغيل سريع
├── requirements.txt       # المكتبات المطلوبة
├── README.md              # دليل شامل
├── INSTRUCTIONS.md        # هذا الملف
├── data/                  # مجلد البيانات
├── models/                # مجلد النماذج المحفوظة
├── logs/                  # مجلد السجلات
└── cache/                 # مجلد التخزين المؤقت
```

## 🎉 نصائح للاستخدام الأمثل

1. **ابدأ بفترة قصيرة**: جرب التنبؤ ليوم واحد أولاً
2. **قارن النماذج**: انظر لتنبؤات Random Forest و XGBoost
3. **راقب مستوى الثقة**: كلما زاد كان التنبؤ أكثر موثوقية
4. **استخدم المؤشرات الفنية**: لفهم اتجاه السوق
5. **حدث البيانات بانتظام**: للحصول على أفضل النتائج

## 📞 الدعم

- راجع ملف `README.md` للمزيد من التفاصيل
- شغل `test_app.py` لاختبار النظام
- تحقق من مجلد `logs/` للأخطاء

---

**تذكير**: هذه أداة تعليمية وتحليلية. استخدمها بحكمة ولا تعتمد عليها وحدها في اتخاذ القرارات الاستثمارية.

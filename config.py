"""
إعدادات التطبيق الرئيسية
"""

import os
from datetime import datetime, timedelta

# إعدادات البيانات
DATA_CONFIG = {
    'symbols': [
        'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA',
        'META', 'NVDA', 'NFLX', 'BABA', 'AMD'
    ],
    'period': '2y',  # فترة جمع البيانات
    'interval': '1d',  # فترة زمنية للبيانات
    'features': [
        'Open', 'High', 'Low', 'Close', 'Volume',
        'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26',
        'RSI', 'MACD', 'BB_upper', 'BB_lower', 'ATR'
    ]
}

# إعدادات النماذج
MODEL_CONFIG = {
    'lstm': {
        'sequence_length': 60,
        'epochs': 100,
        'batch_size': 32,
        'units': [50, 50, 50],
        'dropout': 0.2
    },
    'random_forest': {
        'n_estimators': 100,
        'max_depth': 10,
        'random_state': 42
    },
    'xgboost': {
        'n_estimators': 100,
        'max_depth': 6,
        'learning_rate': 0.1,
        'random_state': 42
    }
}

# إعدادات التطبيق
APP_CONFIG = {
    'title': 'أداة التنبؤ الذكية للأسهم',
    'page_icon': '📈',
    'layout': 'wide',
    'sidebar_state': 'expanded'
}

# مسارات الملفات
PATHS = {
    'data': 'data/',
    'models': 'models/',
    'logs': 'logs/',
    'cache': 'cache/'
}

# إنشاء المجلدات إذا لم تكن موجودة
for path in PATHS.values():
    os.makedirs(path, exist_ok=True)

# إعدادات التنبؤ
PREDICTION_CONFIG = {
    'days_ahead': [1, 3, 7, 14, 30],
    'confidence_threshold': 0.7,
    'update_frequency': 'daily'
}

# رسائل النظام
MESSAGES = {
    'ar': {
        'loading': 'جاري التحميل...',
        'error': 'حدث خطأ',
        'success': 'تم بنجاح',
        'no_data': 'لا توجد بيانات متاحة',
        'prediction_ready': 'التنبؤ جاهز',
        'model_training': 'جاري تدريب النموذج...',
        'data_updating': 'جاري تحديث البيانات...'
    },
    'en': {
        'loading': 'Loading...',
        'error': 'Error occurred',
        'success': 'Success',
        'no_data': 'No data available',
        'prediction_ready': 'Prediction ready',
        'model_training': 'Training model...',
        'data_updating': 'Updating data...'
    }
}

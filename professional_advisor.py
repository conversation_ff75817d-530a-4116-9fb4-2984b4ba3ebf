"""
نظام النصائح الاحترافية للصفقات
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import ta

class ProfessionalTradingAdvisor:
    """
    مستشار التداول الاحترافي
    """
    def __init__(self):
        self.risk_levels = {
            'conservative': {'max_risk': 0.02, 'position_size': 0.01},
            'moderate': {'max_risk': 0.05, 'position_size': 0.02},
            'aggressive': {'max_risk': 0.10, 'position_size': 0.05}
        }
    
    def generate_professional_advice(self, data, analysis, symbol, risk_profile='moderate'):
        """
        إنتاج نصائح احترافية شاملة للتداول
        """
        try:
            advice = {
                'entry_strategy': self.create_entry_strategy(data, analysis),
                'exit_strategy': self.create_exit_strategy(data, analysis),
                'risk_management': self.create_risk_management_plan(data, analysis, risk_profile),
                'position_sizing': self.calculate_position_sizing(data, analysis, risk_profile),
                'timing_advice': self.provide_timing_advice(data, analysis),
                'market_conditions': self.assess_market_conditions(data, analysis),
                'trade_setup': self.create_trade_setup(data, analysis),
                'contingency_plans': self.create_contingency_plans(data, analysis),
                'performance_expectations': self.set_performance_expectations(data, analysis)
            }
            
            return advice
            
        except Exception as e:
            print(f"خطأ في إنتاج النصائح الاحترافية: {str(e)}")
            return {}
    
    def create_entry_strategy(self, data, analysis):
        """
        إنشاء استراتيجية الدخول الاحترافية
        """
        try:
            entry_strategy = {
                'primary_entry': {},
                'secondary_entry': {},
                'confirmation_signals': [],
                'entry_conditions': [],
                'invalidation_levels': {}
            }
            
            current_price = data['Close'].iloc[-1]
            
            # تحليل الاتجاه العام
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis']
                primary_trend = trend.get('primary_trend', 'جانبي')
                trend_confidence = trend.get('trend_confidence', 0.5)
                
                if 'صاعد' in primary_trend and trend_confidence > 0.7:
                    entry_strategy['primary_entry'] = {
                        'direction': 'شراء',
                        'method': 'اختراق المقاومة',
                        'entry_price': self.calculate_breakout_entry(data, 'bullish'),
                        'confidence': trend_confidence,
                        'reasoning': 'اتجاه صاعد قوي مع ثقة عالية'
                    }
                    
                    # نقطة دخول ثانوية
                    entry_strategy['secondary_entry'] = {
                        'direction': 'شراء',
                        'method': 'ارتداد من الدعم',
                        'entry_price': self.find_pullback_entry(data, analysis, 'bullish'),
                        'confidence': trend_confidence * 0.8,
                        'reasoning': 'دخول عند الارتداد من مستوى الدعم'
                    }
                
                elif 'هابط' in primary_trend and trend_confidence > 0.7:
                    entry_strategy['primary_entry'] = {
                        'direction': 'بيع',
                        'method': 'كسر الدعم',
                        'entry_price': self.calculate_breakout_entry(data, 'bearish'),
                        'confidence': trend_confidence,
                        'reasoning': 'اتجاه هابط قوي مع ثقة عالية'
                    }
                    
                    entry_strategy['secondary_entry'] = {
                        'direction': 'بيع',
                        'method': 'ارتداد من المقاومة',
                        'entry_price': self.find_pullback_entry(data, analysis, 'bearish'),
                        'confidence': trend_confidence * 0.8,
                        'reasoning': 'دخول عند الارتداد من مستوى المقاومة'
                    }
                
                else:
                    entry_strategy['primary_entry'] = {
                        'direction': 'انتظار',
                        'method': 'مراقبة الاختراق',
                        'entry_price': current_price,
                        'confidence': 0.5,
                        'reasoning': 'السوق في حالة جانبية - انتظار إشارة واضحة'
                    }
            
            # إشارات التأكيد
            entry_strategy['confirmation_signals'] = self.get_confirmation_signals(data, analysis)
            
            # شروط الدخول
            entry_strategy['entry_conditions'] = self.define_entry_conditions(data, analysis)
            
            # مستويات الإلغاء
            entry_strategy['invalidation_levels'] = self.calculate_invalidation_levels(data, analysis)
            
            return entry_strategy
            
        except Exception as e:
            return {'error': str(e)}
    
    def create_exit_strategy(self, data, analysis):
        """
        إنشاء استراتيجية الخروج الاحترافية
        """
        try:
            exit_strategy = {
                'profit_targets': {},
                'stop_loss': {},
                'trailing_stop': {},
                'partial_exits': [],
                'exit_signals': []
            }
            
            current_price = data['Close'].iloc[-1]
            atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close']).iloc[-1]
            
            # أهداف الربح
            exit_strategy['profit_targets'] = {
                'target_1': {
                    'price': current_price * 1.02,  # 2%
                    'percentage': 50,  # إغلاق 50% من المركز
                    'reasoning': 'هدف قريب للحصول على ربح سريع'
                },
                'target_2': {
                    'price': current_price * 1.05,  # 5%
                    'percentage': 30,  # إغلاق 30% إضافية
                    'reasoning': 'هدف متوسط بناءً على التحليل الفني'
                },
                'target_3': {
                    'price': current_price * 1.10,  # 10%
                    'percentage': 20,  # إغلاق الباقي
                    'reasoning': 'هدف طويل المدى للحصول على أقصى ربح'
                }
            }
            
            # وقف الخسارة
            stop_loss_price = current_price - (atr * 2)
            exit_strategy['stop_loss'] = {
                'initial_stop': stop_loss_price,
                'risk_percentage': ((current_price - stop_loss_price) / current_price) * 100,
                'type': 'وقف خسارة ثابت',
                'reasoning': 'بناءً على ATR لتجنب الخروج المبكر من التقلبات الطبيعية'
            }
            
            # وقف الخسارة المتحرك
            exit_strategy['trailing_stop'] = {
                'activation_price': current_price * 1.03,  # يبدأ عند ربح 3%
                'trail_amount': atr * 1.5,
                'type': 'وقف متحرك بناءً على ATR',
                'reasoning': 'للحفاظ على الأرباح مع السماح للسعر بالنمو'
            }
            
            # خروج جزئي
            exit_strategy['partial_exits'] = [
                {
                    'condition': 'RSI > 80',
                    'action': 'إغلاق 25% من المركز',
                    'reasoning': 'تشبع شرائي قوي'
                },
                {
                    'condition': 'كسر المتوسط المتحرك 20',
                    'action': 'إغلاق 50% من المركز',
                    'reasoning': 'ضعف في الاتجاه'
                }
            ]
            
            # إشارات الخروج
            exit_strategy['exit_signals'] = self.identify_exit_signals(data, analysis)
            
            return exit_strategy
            
        except Exception as e:
            return {'error': str(e)}
    
    def create_risk_management_plan(self, data, analysis, risk_profile):
        """
        إنشاء خطة إدارة المخاطر
        """
        try:
            risk_plan = {
                'risk_assessment': {},
                'position_limits': {},
                'diversification': {},
                'correlation_analysis': {},
                'stress_testing': {}
            }
            
            # تقييم المخاطر
            volatility = data['Close'].pct_change().std() * np.sqrt(252)  # التقلبات السنوية
            max_drawdown = self.calculate_max_drawdown(data['Close'])
            
            risk_plan['risk_assessment'] = {
                'volatility': volatility,
                'max_drawdown': max_drawdown,
                'risk_level': self.classify_risk_level(volatility, max_drawdown),
                'var_95': self.calculate_var(data['Close'], 0.05),
                'expected_shortfall': self.calculate_expected_shortfall(data['Close'], 0.05)
            }
            
            # حدود المركز
            max_risk = self.risk_levels[risk_profile]['max_risk']
            risk_plan['position_limits'] = {
                'max_position_size': self.risk_levels[risk_profile]['position_size'],
                'max_risk_per_trade': max_risk,
                'max_correlation_exposure': 0.3,
                'max_sector_exposure': 0.2
            }
            
            # التنويع
            risk_plan['diversification'] = {
                'recommended_positions': self.calculate_optimal_positions(risk_profile),
                'correlation_threshold': 0.7,
                'rebalancing_frequency': 'أسبوعي',
                'sector_allocation': self.suggest_sector_allocation(risk_profile)
            }
            
            return risk_plan
            
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_position_sizing(self, data, analysis, risk_profile):
        """
        حساب حجم المركز الأمثل
        """
        try:
            position_sizing = {}
            
            current_price = data['Close'].iloc[-1]
            atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close']).iloc[-1]
            
            # طريقة ATR
            max_risk = self.risk_levels[risk_profile]['max_risk']
            atr_position_size = max_risk / (atr / current_price)
            
            # طريقة التقلبات
            volatility = data['Close'].pct_change().std()
            volatility_position_size = (max_risk / 2) / volatility
            
            # طريقة Kelly Criterion (مبسطة)
            win_rate = 0.6  # افتراضي
            avg_win = 0.03
            avg_loss = 0.02
            kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
            kelly_position_size = min(kelly_fraction, max_risk * 2)
            
            # المتوسط المرجح
            weights = [0.4, 0.3, 0.3]  # ATR, Volatility, Kelly
            sizes = [atr_position_size, volatility_position_size, kelly_position_size]
            optimal_size = np.average(sizes, weights=weights)
            
            # تطبيق حدود الأمان
            optimal_size = min(optimal_size, self.risk_levels[risk_profile]['position_size'])
            optimal_size = max(optimal_size, 0.005)  # حد أدنى 0.5%
            
            position_sizing = {
                'recommended_size': optimal_size,
                'size_percentage': f"{optimal_size * 100:.2f}%",
                'calculation_methods': {
                    'atr_method': atr_position_size,
                    'volatility_method': volatility_position_size,
                    'kelly_method': kelly_position_size
                },
                'risk_amount': optimal_size * max_risk,
                'shares_to_buy': int((10000 * optimal_size) / current_price),  # افتراض رأس مال 10000
                'reasoning': f'حساب متوسط مرجح بناءً على ATR والتقلبات ومعيار Kelly مع مراعاة ملف المخاطر {risk_profile}'
            }
            
            return position_sizing
            
        except Exception as e:
            return {'error': str(e)}
    
    def provide_timing_advice(self, data, analysis):
        """
        تقديم نصائح التوقيت
        """
        try:
            timing_advice = {
                'best_entry_time': {},
                'market_sessions': {},
                'volatility_patterns': {},
                'news_considerations': {}
            }
            
            # تحليل أفضل أوقات الدخول
            current_hour = datetime.now().hour
            
            if 9 <= current_hour <= 11:
                timing_advice['best_entry_time'] = {
                    'recommendation': 'وقت ممتاز للدخول',
                    'reason': 'ساعات الافتتاح - حجم تداول عالي وحركة قوية',
                    'volatility': 'عالية',
                    'liquidity': 'ممتازة'
                }
            elif 14 <= current_hour <= 16:
                timing_advice['best_entry_time'] = {
                    'recommendation': 'وقت جيد للدخول',
                    'reason': 'فترة بعد الظهر - استقرار نسبي مع حجم جيد',
                    'volatility': 'متوسطة',
                    'liquidity': 'جيدة'
                }
            else:
                timing_advice['best_entry_time'] = {
                    'recommendation': 'توخي الحذر',
                    'reason': 'ساعات هادئة - حجم منخفض وسيولة محدودة',
                    'volatility': 'منخفضة',
                    'liquidity': 'محدودة'
                }
            
            # جلسات السوق
            timing_advice['market_sessions'] = {
                'asian_session': 'هادئة - مناسبة للاستراتيجيات طويلة المدى',
                'european_session': 'متوسطة النشاط - جيدة للتداول المتوسط',
                'us_session': 'عالية النشاط - مثالية للتداول قصير المدى',
                'overlap_periods': 'أفضل الأوقات للتداول - سيولة عالية'
            }
            
            return timing_advice
            
        except Exception as e:
            return {'error': str(e)}
    
    def create_trade_setup(self, data, analysis):
        """
        إنشاء إعداد الصفقة الكامل
        """
        try:
            trade_setup = {
                'setup_type': '',
                'entry_checklist': [],
                'risk_reward_ratio': 0,
                'probability_of_success': 0,
                'trade_duration': '',
                'market_bias': ''
            }
            
            # تحديد نوع الإعداد
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis']['primary_trend']
                if 'صاعد' in trend:
                    trade_setup['setup_type'] = 'اتجاه صاعد - Trend Following'
                    trade_setup['market_bias'] = 'صاعد'
                elif 'هابط' in trend:
                    trade_setup['setup_type'] = 'اتجاه هابط - Trend Following'
                    trade_setup['market_bias'] = 'هابط'
                else:
                    trade_setup['setup_type'] = 'تداول المدى - Range Trading'
                    trade_setup['market_bias'] = 'جانبي'
            
            # قائمة فحص الدخول
            trade_setup['entry_checklist'] = [
                '✓ تأكيد الاتجاه من عدة إطارات زمنية',
                '✓ إشارة زخم إيجابية (RSI, MACD)',
                '✓ حجم تداول مؤكد',
                '✓ مستوى دعم/مقاومة واضح',
                '✓ نسبة مخاطر/عائد مقبولة (1:2 على الأقل)',
                '✓ ظروف السوق مناسبة',
                '✓ عدم وجود أخبار مهمة متوقعة'
            ]
            
            # نسبة المخاطر/العائد
            current_price = data['Close'].iloc[-1]
            atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close']).iloc[-1]
            
            stop_loss = atr * 2
            take_profit = atr * 4
            trade_setup['risk_reward_ratio'] = take_profit / stop_loss
            
            # احتمالية النجاح
            trade_setup['probability_of_success'] = self.estimate_success_probability(data, analysis)
            
            # مدة الصفقة المتوقعة
            if trade_setup['setup_type'] == 'تداول المدى':
                trade_setup['trade_duration'] = '1-3 أيام'
            else:
                trade_setup['trade_duration'] = '3-7 أيام'
            
            return trade_setup
            
        except Exception as e:
            return {'error': str(e)}
    
    # الوظائف المساعدة
    def calculate_breakout_entry(self, data, direction):
        """حساب نقطة دخول الاختراق"""
        if direction == 'bullish':
            return data['High'].tail(20).max() * 1.001  # اختراق أعلى قمة
        else:
            return data['Low'].tail(20).min() * 0.999   # كسر أدنى قاع
    
    def find_pullback_entry(self, data, analysis, direction):
        """العثور على نقطة دخول الارتداد"""
        current_price = data['Close'].iloc[-1]
        if direction == 'bullish':
            return current_price * 0.98  # ارتداد 2%
        else:
            return current_price * 1.02  # ارتداد 2%
    
    def get_confirmation_signals(self, data, analysis):
        """الحصول على إشارات التأكيد"""
        signals = []
        
        # RSI
        rsi = ta.momentum.rsi(data['Close'], window=14).iloc[-1]
        if 30 < rsi < 70:
            signals.append('RSI في منطقة متوازنة')
        
        # MACD
        macd = ta.trend.macd(data['Close']).iloc[-1]
        macd_signal = ta.trend.macd_signal(data['Close']).iloc[-1]
        if macd > macd_signal:
            signals.append('MACD إيجابي')
        
        # الحجم
        volume_sma = ta.trend.sma_indicator(data['Volume'], window=20).iloc[-1]
        if data['Volume'].iloc[-1] > volume_sma:
            signals.append('حجم تداول مرتفع')
        
        return signals
    
    def define_entry_conditions(self, data, analysis):
        """تحديد شروط الدخول"""
        conditions = [
            'اختراق مستوى المقاومة بحجم مرتفع',
            'إغلاق فوق المتوسط المتحرك 20',
            'RSI فوق 50 ولكن أقل من 80',
            'MACD فوق خط الإشارة',
            'عدم وجود تباعد سلبي'
        ]
        return conditions
    
    def calculate_invalidation_levels(self, data, analysis):
        """حساب مستويات الإلغاء"""
        current_price = data['Close'].iloc[-1]
        atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close']).iloc[-1]
        
        return {
            'bullish_invalidation': current_price - (atr * 3),
            'bearish_invalidation': current_price + (atr * 3),
            'reasoning': 'بناءً على 3 أضعاف ATR من السعر الحالي'
        }
    
    def identify_exit_signals(self, data, analysis):
        """تحديد إشارات الخروج"""
        signals = [
            'RSI يدخل منطقة التشبع (>80 أو <20)',
            'تباعد بين السعر والمؤشرات',
            'كسر المتوسط المتحرك الرئيسي',
            'انخفاض حجم التداول بشكل كبير',
            'تشكيل نمط انعكاسي'
        ]
        return signals
    
    def calculate_max_drawdown(self, prices):
        """حساب أقصى انخفاض"""
        cumulative = (1 + prices.pct_change()).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def classify_risk_level(self, volatility, max_drawdown):
        """تصنيف مستوى المخاطر"""
        if volatility > 0.4 or max_drawdown < -0.3:
            return 'عالي'
        elif volatility > 0.2 or max_drawdown < -0.15:
            return 'متوسط'
        else:
            return 'منخفض'
    
    def calculate_var(self, prices, confidence):
        """حساب Value at Risk"""
        returns = prices.pct_change().dropna()
        return np.percentile(returns, confidence * 100)
    
    def calculate_expected_shortfall(self, prices, confidence):
        """حساب Expected Shortfall"""
        returns = prices.pct_change().dropna()
        var = self.calculate_var(prices, confidence)
        return returns[returns <= var].mean()
    
    def estimate_success_probability(self, data, analysis):
        """تقدير احتمالية النجاح"""
        # تحليل مبسط لاحتمالية النجاح
        score = 0.5
        
        # قوة الاتجاه
        if 'trend_analysis' in analysis:
            confidence = analysis['trend_analysis'].get('trend_confidence', 0.5)
            score += (confidence - 0.5) * 0.4
        
        # إشارات الزخم
        rsi = ta.momentum.rsi(data['Close'], window=14).iloc[-1]
        if 40 < rsi < 60:  # منطقة متوازنة
            score += 0.1
        
        return min(0.9, max(0.1, score))

# مثال على الاستخدام
if __name__ == "__main__":
    print("💡 نظام النصائح الاحترافية للصفقات جاهز!")

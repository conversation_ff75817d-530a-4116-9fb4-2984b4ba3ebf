"""
مولد الرسوم البيانية المتقدمة والاحترافية
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import ta
from datetime import datetime

class AdvancedChartGenerator:
    """
    مولد الرسوم البيانية المتقدمة
    """
    def __init__(self):
        self.colors = {
            'bullish': '#00ff88',
            'bearish': '#ff4444',
            'neutral': '#ffaa00',
            'background': '#1e1e1e',
            'grid': '#333333',
            'text': '#ffffff'
        }
    
    def create_comprehensive_chart(self, data, analysis, symbol):
        """
        إنشاء رسم بياني شامل ومتقدم
        """
        try:
            # إنشاء subplots
            fig = make_subplots(
                rows=4, cols=2,
                shared_xaxes=True,
                vertical_spacing=0.03,
                horizontal_spacing=0.05,
                subplot_titles=[
                    'السعر والمؤشرات الرئيسية', 'مستويات الدعم والمقاومة',
                    'مؤشرات الزخم', 'تحليل الحجم',
                    'مؤشرات التقلبات', 'التحليل المتقدم',
                    'نقاط الدخول والخروج', 'ملخص الإشارات'
                ],
                specs=[
                    [{"secondary_y": True}, {"secondary_y": False}],
                    [{"secondary_y": False}, {"secondary_y": True}],
                    [{"secondary_y": False}, {"secondary_y": False}],
                    [{"colspan": 2}, None]
                ],
                row_heights=[0.4, 0.25, 0.25, 0.1]
            )
            
            # الرسم الرئيسي - الشموع اليابانية
            self.add_candlestick_chart(fig, data, row=1, col=1)
            
            # المتوسطات المتحركة
            self.add_moving_averages(fig, data, row=1, col=1)
            
            # Bollinger Bands
            self.add_bollinger_bands(fig, data, row=1, col=1)
            
            # مستويات الدعم والمقاومة
            self.add_support_resistance(fig, data, analysis, row=1, col=2)
            
            # مؤشرات الزخم
            self.add_momentum_indicators(fig, data, row=2, col=1)
            
            # تحليل الحجم
            self.add_volume_analysis(fig, data, row=2, col=2)
            
            # مؤشرات التقلبات
            self.add_volatility_indicators(fig, data, row=3, col=1)
            
            # التحليل المتقدم
            self.add_advanced_analysis(fig, data, analysis, row=3, col=2)
            
            # نقاط الدخول والخروج
            self.add_entry_exit_signals(fig, data, analysis, row=4, col=1)
            
            # تنسيق الرسم
            self.format_chart(fig, symbol)
            
            return fig
            
        except Exception as e:
            print(f"خطأ في إنشاء الرسم البياني: {str(e)}")
            return None
    
    def add_candlestick_chart(self, fig, data, row, col):
        """
        إضافة رسم الشموع اليابانية
        """
        fig.add_trace(
            go.Candlestick(
                x=data.index,
                open=data['Open'],
                high=data['High'],
                low=data['Low'],
                close=data['Close'],
                name='السعر',
                increasing_line_color=self.colors['bullish'],
                decreasing_line_color=self.colors['bearish'],
                increasing_fillcolor=self.colors['bullish'],
                decreasing_fillcolor=self.colors['bearish']
            ),
            row=row, col=col
        )
    
    def add_moving_averages(self, fig, data, row, col):
        """
        إضافة المتوسطات المتحركة
        """
        # SMA
        sma_20 = ta.trend.sma_indicator(data['Close'], window=20)
        sma_50 = ta.trend.sma_indicator(data['Close'], window=50)
        sma_200 = ta.trend.sma_indicator(data['Close'], window=200)
        
        # EMA
        ema_12 = ta.trend.ema_indicator(data['Close'], window=12)
        ema_26 = ta.trend.ema_indicator(data['Close'], window=26)
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=sma_20,
                mode='lines', name='SMA 20',
                line=dict(color='orange', width=1)
            ),
            row=row, col=col
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=sma_50,
                mode='lines', name='SMA 50',
                line=dict(color='red', width=1)
            ),
            row=row, col=col
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=sma_200,
                mode='lines', name='SMA 200',
                line=dict(color='purple', width=2)
            ),
            row=row, col=col
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=ema_12,
                mode='lines', name='EMA 12',
                line=dict(color='cyan', width=1, dash='dash')
            ),
            row=row, col=col
        )
    
    def add_bollinger_bands(self, fig, data, row, col):
        """
        إضافة Bollinger Bands
        """
        bb_high = ta.volatility.bollinger_hband(data['Close'])
        bb_low = ta.volatility.bollinger_lband(data['Close'])
        bb_mid = ta.volatility.bollinger_mavg(data['Close'])
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=bb_high,
                mode='lines', name='BB Upper',
                line=dict(color='rgba(255,255,255,0.3)', width=1),
                showlegend=False
            ),
            row=row, col=col
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=bb_low,
                mode='lines', name='BB Lower',
                line=dict(color='rgba(255,255,255,0.3)', width=1),
                fill='tonexty', fillcolor='rgba(255,255,255,0.1)',
                showlegend=False
            ),
            row=row, col=col
        )
    
    def add_support_resistance(self, fig, data, analysis, row, col):
        """
        إضافة مستويات الدعم والمقاومة
        """
        if 'support_resistance' in analysis:
            sr = analysis['support_resistance']
            
            # Pivot Points
            if 'pivot_points' in sr:
                pivot = sr['pivot_points']
                current_price = data['Close'].iloc[-1]
                
                # خطوط أفقية للمستويات
                levels = [
                    ('المقاومة 2', pivot.get('r2', current_price * 1.04), 'red'),
                    ('المقاومة 1', pivot.get('r1', current_price * 1.02), 'orange'),
                    ('النقطة المحورية', pivot.get('pivot', current_price), 'yellow'),
                    ('الدعم 1', pivot.get('s1', current_price * 0.98), 'lightgreen'),
                    ('الدعم 2', pivot.get('s2', current_price * 0.96), 'green')
                ]
                
                for name, level, color in levels:
                    fig.add_hline(
                        y=level, line_dash="dash",
                        line_color=color, line_width=1,
                        annotation_text=f"{name}: {level:.2f}",
                        row=row, col=col
                    )
    
    def add_momentum_indicators(self, fig, data, row, col):
        """
        إضافة مؤشرات الزخم
        """
        # RSI
        rsi = ta.momentum.rsi(data['Close'], window=14)
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=rsi,
                mode='lines', name='RSI',
                line=dict(color='purple', width=2)
            ),
            row=row, col=col
        )
        
        # خطوط RSI
        fig.add_hline(y=70, line_dash="dash", line_color="red", 
                     annotation_text="تشبع شرائي", row=row, col=col)
        fig.add_hline(y=30, line_dash="dash", line_color="green", 
                     annotation_text="تشبع بيعي", row=row, col=col)
        fig.add_hline(y=50, line_dash="dot", line_color="gray", row=row, col=col)
    
    def add_volume_analysis(self, fig, data, row, col):
        """
        إضافة تحليل الحجم
        """
        # حجم التداول
        colors = ['green' if close >= open else 'red' 
                 for close, open in zip(data['Close'], data['Open'])]
        
        fig.add_trace(
            go.Bar(
                x=data.index, y=data['Volume'],
                name='الحجم', marker_color=colors,
                opacity=0.7
            ),
            row=row, col=col
        )
        
        # متوسط الحجم
        volume_sma = ta.trend.sma_indicator(data['Volume'], window=20)
        fig.add_trace(
            go.Scatter(
                x=data.index, y=volume_sma,
                mode='lines', name='متوسط الحجم',
                line=dict(color='orange', width=2)
            ),
            row=row, col=col, secondary_y=True
        )
    
    def add_volatility_indicators(self, fig, data, row, col):
        """
        إضافة مؤشرات التقلبات
        """
        # ATR
        atr = ta.volatility.average_true_range(data['High'], data['Low'], data['Close'])
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=atr,
                mode='lines', name='ATR',
                line=dict(color='orange', width=2)
            ),
            row=row, col=col
        )
        
        # Bollinger Band Width
        bb_width = ta.volatility.bollinger_wband(data['Close'])
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=bb_width,
                mode='lines', name='BB Width',
                line=dict(color='cyan', width=1)
            ),
            row=row, col=col
        )
    
    def add_advanced_analysis(self, fig, data, analysis, row, col):
        """
        إضافة التحليل المتقدم
        """
        # MACD
        macd_line = ta.trend.macd(data['Close'])
        macd_signal = ta.trend.macd_signal(data['Close'])
        macd_histogram = ta.trend.macd_diff(data['Close'])
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=macd_line,
                mode='lines', name='MACD',
                line=dict(color='blue', width=2)
            ),
            row=row, col=col
        )
        
        fig.add_trace(
            go.Scatter(
                x=data.index, y=macd_signal,
                mode='lines', name='Signal',
                line=dict(color='red', width=1)
            ),
            row=row, col=col
        )
        
        # MACD Histogram
        colors = ['green' if val >= 0 else 'red' for val in macd_histogram]
        fig.add_trace(
            go.Bar(
                x=data.index, y=macd_histogram,
                name='MACD Histogram',
                marker_color=colors, opacity=0.6
            ),
            row=row, col=col
        )
    
    def add_entry_exit_signals(self, fig, data, analysis, row, col):
        """
        إضافة نقاط الدخول والخروج
        """
        # محاكاة إشارات الدخول والخروج
        entry_points = []
        exit_points = []
        
        # تحليل بسيط للإشارات
        rsi = ta.momentum.rsi(data['Close'], window=14)
        sma_20 = ta.trend.sma_indicator(data['Close'], window=20)
        
        for i in range(20, len(data)):
            # إشارة شراء: RSI < 30 والسعر فوق SMA 20
            if rsi.iloc[i] < 30 and data['Close'].iloc[i] > sma_20.iloc[i]:
                entry_points.append((data.index[i], data['Close'].iloc[i]))
            
            # إشارة بيع: RSI > 70
            elif rsi.iloc[i] > 70:
                exit_points.append((data.index[i], data['Close'].iloc[i]))
        
        # رسم نقاط الدخول
        if entry_points:
            entry_x, entry_y = zip(*entry_points)
            fig.add_trace(
                go.Scatter(
                    x=entry_x, y=entry_y,
                    mode='markers', name='نقاط دخول',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,
                        color='green'
                    )
                ),
                row=1, col=1
            )
        
        # رسم نقاط الخروج
        if exit_points:
            exit_x, exit_y = zip(*exit_points)
            fig.add_trace(
                go.Scatter(
                    x=exit_x, y=exit_y,
                    mode='markers', name='نقاط خروج',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,
                        color='red'
                    )
                ),
                row=1, col=1
            )
    
    def format_chart(self, fig, symbol):
        """
        تنسيق الرسم البياني
        """
        fig.update_layout(
            title={
                'text': f'تحليل شامل ومتقدم - {symbol}',
                'x': 0.5,
                'font': {'size': 20, 'color': self.colors['text']}
            },
            paper_bgcolor=self.colors['background'],
            plot_bgcolor=self.colors['background'],
            font=dict(color=self.colors['text']),
            height=1200,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        # تحديث محاور X
        fig.update_xaxes(
            gridcolor=self.colors['grid'],
            showgrid=True,
            rangeslider_visible=False
        )
        
        # تحديث محاور Y
        fig.update_yaxes(
            gridcolor=self.colors['grid'],
            showgrid=True
        )
    
    def create_signal_summary_chart(self, analysis):
        """
        إنشاء رسم ملخص الإشارات
        """
        try:
            # استخراج الإشارات
            signals = []
            strengths = []
            colors = []
            
            if 'overall_signal' in analysis:
                overall = analysis['overall_signal']
                signal = overall.get('signal', 'غير محدد')
                strength = overall.get('strength', 0)
                
                signals.append('الإشارة العامة')
                strengths.append(strength * 100)
                
                if 'شراء' in signal:
                    colors.append(self.colors['bullish'])
                elif 'بيع' in signal:
                    colors.append(self.colors['bearish'])
                else:
                    colors.append(self.colors['neutral'])
            
            # إضافة إشارات أخرى
            if 'trend_analysis' in analysis:
                trend = analysis['trend_analysis']
                confidence = trend.get('trend_confidence', 0)
                signals.append('قوة الاتجاه')
                strengths.append(confidence * 100)
                colors.append(self.colors['bullish'] if confidence > 0.6 else self.colors['neutral'])
            
            if 'momentum_analysis' in analysis:
                momentum = analysis['momentum_analysis']
                score = momentum.get('momentum_score', 0)
                signals.append('الزخم')
                strengths.append(abs(score) * 100)
                colors.append(self.colors['bullish'] if score > 0 else self.colors['bearish'])
            
            # إنشاء الرسم
            fig = go.Figure(data=[
                go.Bar(
                    x=signals,
                    y=strengths,
                    marker_color=colors,
                    text=[f'{s:.1f}%' for s in strengths],
                    textposition='auto'
                )
            ])
            
            fig.update_layout(
                title='ملخص قوة الإشارات',
                paper_bgcolor=self.colors['background'],
                plot_bgcolor=self.colors['background'],
                font=dict(color=self.colors['text']),
                height=400
            )
            
            return fig
            
        except Exception as e:
            print(f"خطأ في إنشاء ملخص الإشارات: {str(e)}")
            return None

# مثال على الاستخدام
if __name__ == "__main__":
    print("📊 مولد الرسوم البيانية المتقدمة جاهز!")
